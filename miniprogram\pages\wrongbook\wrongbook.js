const app = getApp();
import createHanziWriterContext from 'hanzi-writer-miniprogram';
Page({
  data: {
    // 错题数据
    pendingErrorsByCourse: [], // 待处理错题（按课程分组）
    historyErrorsByCourse: [], // 历史错题（按课程分组）

    // 标签页
    activeTab: 0, // 0: 待处理，1: 历史记录

    // 选择状态
    selectedWords: [], // 选中的待处理错题
    allSelected: false,

    // 统计信息
    totalPendingErrors: 0,
    totalHistoryErrors: 0,

    // 刷新状态
    refreshing: false,

    // 笔画动画弹窗
    showStrokeModal: false,
    currentCharacter: '',
    currentCharacterIndex: 0,
    currentWord: '',
    isAutoPlaying: false // 新增：标记是否在自动播放模式
    // 注意：writerCtx不存储在data中，避免循环引用问题
  },

  onLoad() {
    console.log('错题集页面加载');
    
  },

  onShow() {
    console.log('错题集页面显示');

    // 每次显示页面时重新加载错题数据，确保按当前教材筛选
    this.loadErrorWords();

    // 检查是否有错题复习结果
    this.checkErrorReviewResult();

    // 如果笔画弹窗是打开的，重新绘制田字格
    if (this.data.showStrokeModal) {
      setTimeout(() => {
        this.forceRedrawGrid();
      }, 500);
    }
  },

  // 页面初次渲染完成
  onReady() {
    console.log('页面渲染完成');
    // 页面渲染完成后，如果弹窗是打开的，确保田字格显示
    if (this.data.showStrokeModal) {
      setTimeout(() => {
        console.log('onReady中重绘田字格');
        this.checkCanvasExists();
        this.drawBasicGrid();
      }, 100);
    }
  },

  onUnload() {
    console.log('错题集页面卸载');
    // 清理汉字书写器实例
    if (this._writerCtx) {
      try {
        if (this._writerCtx.destroy) {
          this._writerCtx.destroy();
        }
      } catch (e) {
        console.warn('页面卸载时清理汉字书写器实例失败:', e);
      }
      this._writerCtx = null;
    }
    this._isInitializing = false;
  },

  onHide() {
    console.log('错题集页面隐藏');
    // 如果弹窗打开，关闭它
    if (this.data.showStrokeModal) {
      this.onCloseStrokeModal();
    }
  },

  // 加载错题数据
  loadErrorWords() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    console.log('=== 错题集数据加载开始 ===');
    console.log('原始错题数据总数:', errorWords.length);
    console.log('原始错题数据详情:', errorWords);

    // 获取当前选择的教材信息
    const currentTextbook = wx.getStorageSync('currentTextbook') || {};
    console.log('当前选择的教材信息:', currentTextbook);

    // 筛选当前教材版本的错题
    const filteredErrorWords = errorWords.filter(error => {
      // 如果没有选择教材或错题没有课程信息，则显示所有错题
      if (!currentTextbook.publisher || !error.courseInfo) return true;

      // 检查是否匹配当前教材版本、年级和学期
      const matchPublisher = error.courseInfo.publisherId === currentTextbook.publisher.id;
      const matchGrade = error.courseInfo.gradeId === currentTextbook.grade.id;
      const matchTerm = error.courseInfo.term === currentTextbook.term;

      return matchPublisher && matchGrade && matchTerm;
    });

    console.log('筛选后的错题数据总数:', filteredErrorWords.length);

    // 分析每个错题的状态
    const correctedErrors = [];
    const uncorrectedErrors = [];

    filteredErrorWords.forEach((error, index) => {
      console.log(`\n错题 ${index + 1}:`);
      console.log(`  字词: ${error.word}`);
      console.log(`  课程: ${error.courseInfo?.courseName || error.courseInfo?.courseId || '未知课程'}`);
      console.log(`  订正状态: ${error.corrected ? '已订正' : '未订正'}`);
      console.log(`  订正时间: ${error.correctedTime || '无'}`);

      if (error.corrected === true) {
        correctedErrors.push(error);
      } else {
        uncorrectedErrors.push(error);
      }
    });

    console.log(`\n状态统计:`);
    console.log(`  已订正: ${correctedErrors.length} 个`);
    console.log(`  未订正: ${uncorrectedErrors.length} 个`);

    // 重新定义分类逻辑
    // 待处理：只显示未订正的错题
    const pendingErrors = filteredErrorWords.filter(error => !error.corrected || error.corrected === false);
    // 历史记录：显示所有错题（包括已订正和未订正）
    const historyErrors = [...filteredErrorWords]; // 显示所有错题

    console.log('\n=== 分类结果 ===');
    console.log('待处理错题（未订正）:', pendingErrors.length, '个');
    console.log('待处理错题详情:', pendingErrors.map(e => `${e.word}(${e.corrected ? '已订正' : '未订正'})`));
    console.log('历史错题（所有）:', historyErrors.length, '个');

    // 按课程分组
    const pendingErrorsByCourse = this.groupErrorsByCourse(pendingErrors);
    const historyErrorsByCourse = this.groupErrorsByCourse(historyErrors);

    console.log('\n=== 按课程分组结果 ===');
    console.log('待处理错题（按课程）:', pendingErrorsByCourse.length, '个课程');
    pendingErrorsByCourse.forEach((course, index) => {
      console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });

    console.log('历史错题（按课程）:', historyErrorsByCourse.length, '个课程');
    historyErrorsByCourse.forEach((course, index) => {
      console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });

    this.setData({
      pendingErrorsByCourse,
      historyErrorsByCourse,
      totalPendingErrors: pendingErrors.length,
      totalHistoryErrors: historyErrors.length,
      selectedWords: [], // 重置选择
      allSelected: false
    });

    console.log('\n=== 页面数据更新完成 ===');
    console.log('页面状态:');
    console.log(`  totalPendingErrors: ${pendingErrors.length}`);
    console.log(`  totalHistoryErrors: ${historyErrors.length}`);
    console.log(`  pendingErrorsByCourse: ${pendingErrorsByCourse.length} 个课程`);
    console.log('=== 错题集数据加载结束 ===\n');
  },

  // 按课程分组错题
  groupErrorsByCourse(errorWords) {
    const groups = {};

    errorWords.forEach(error => {
      const courseInfo = error.courseInfo;
      let courseKey = 'unknown_course';
      let courseName = '未知课程';

      if (courseInfo) {
        // 优先使用courseId构建key
        if (courseInfo.courseId) {
          courseKey = courseInfo.courseId;
        } else {
          // 备用方案：使用组合ID
          const publisherId = courseInfo.publisherId || 'unknown';
          const gradeId = courseInfo.gradeId || 'unknown';
          const term = courseInfo.term || 'unknown';
          courseKey = `${publisherId}_${gradeId}_${term}`;
        }

        // 构建课程标题 - 优先使用完整信息
        if (courseInfo.courseName) {
          // 如果有完整的课程标题，直接使用
          if (courseInfo.courseTitle) {
            courseName = courseInfo.courseTitle;
          } else {
            // 否则构建标题
            const publisher = courseInfo.publisher || '';
            const grade = courseInfo.grade || '';
            const term = courseInfo.term === 'term1' ? '上册' :
              courseInfo.term === 'term2' ? '下册' :
                courseInfo.term || '';

            // 只有当有具体信息时才构建完整标题
            if (publisher && grade) {
              courseName = `${publisher} ${grade}${term} - ${courseInfo.courseName}`;
            } else if (courseInfo.courseName) {
              // 如果没有出版社和年级信息，只显示课程名称
              courseName = courseInfo.courseName;
            }
          }
        } else {
          // 如果没有课程名称，尝试使用课程ID或显示未知
          courseName = courseInfo.courseId || '未知课程';
        }
      }

      if (!groups[courseKey]) {
        groups[courseKey] = {
          courseKey,
          courseName,
          courseInfo,
          words: [],
          errorCount: 0,
          allSelected: false
        };
      }

      // 确保字词数据完整性并处理拼音格式
      let formattedPinyin = error.pinyin || '';

      console.log(`处理字词 "${error.word}" 的拼音:`, error.pinyin);

      // 处理拼音格式：确保每个字对应一个拼音，用空格分隔
      if (error.word && error.word.length > 0) {
        if (formattedPinyin) {
          // 检查原始拼音格式
          let pinyinParts = formattedPinyin.trim().split(/\s+/); // 使用正则分割多个空格
          const wordLength = error.word.length;

          console.log(`字词长度: ${wordLength}, 拼音部分: ${pinyinParts.length}`, pinyinParts);

          // 如果拼音部分数量与字数不匹配，尝试智能分割
          if (pinyinParts.length !== wordLength) {
            if (pinyinParts.length === 1 && wordLength > 1) {
              // 单个拼音字符串，尝试按拼音规则分割
              const singlePinyin = pinyinParts[0];
              pinyinParts = this.splitPinyinString(singlePinyin, wordLength);
              console.log(`智能分割后的拼音:`, pinyinParts);
            }

            // 如果分割后仍然不匹配，进行长度调整
            if (pinyinParts.length < wordLength) {
              // 补充缺少的拼音部分
              const additionalParts = Array(wordLength - pinyinParts.length).fill('');
              pinyinParts = [...pinyinParts, ...additionalParts];
            } else if (pinyinParts.length > wordLength) {
              // 如果拼音过多，只取前面对应数量的拼音
              pinyinParts = pinyinParts.slice(0, wordLength);
            }
          }

          // 重新组合拼音
          formattedPinyin = pinyinParts.join(' ');
        } else {
          // 如果没有拼音，为每个字符创建空的拼音
          formattedPinyin = Array(error.word.length).fill('').join(' ');
        }
      }

      console.log(`最终拼音格式:`, formattedPinyin);

      const pinyinArray = formattedPinyin ? formattedPinyin.split(' ') : [];
      console.log(`拼音数组:`, pinyinArray);

      const wordData = {
        ...error,
        word: error.word || '未知字词',
        pinyin: formattedPinyin,
        pinyinArray: pinyinArray, // 使用已处理的拼音数组
        selected: false,
        attempts: error.attempts || 1,
        // 格式化时间显示
        lastErrorTime: this.formatTimeAgo(error.lastErrorTime),
        correctedTime: error.correctedTime ? this.formatTimeAgo(error.correctedTime) : null
      };

      groups[courseKey].words.push(wordData);
      groups[courseKey].errorCount++;
    });

    // 按课程分组后的排序处理
    const groupsArray = Object.values(groups);

    // 对每个课程组内的错题进行排序：已订正的排在前面
    groupsArray.forEach(group => {
      group.words.sort((a, b) => {
        // 已订正的排前面，未订正的排后面
        if (a.corrected !== b.corrected) {
          return a.corrected ? -1 : 1;
        }
        // 同样订正状态下，按时间排序（最新的在前）
        const timeA = new Date(a.correctedTime || a.lastErrorTime || a.timestamp || 0);
        const timeB = new Date(b.correctedTime || b.lastErrorTime || b.timestamp || 0);
        return timeB - timeA;
      });
    });

    // 按课程的最新错题时间排序
    return groupsArray.sort((a, b) =>
      new Date(b.words[0]?.timestamp || 0) - new Date(a.words[0]?.timestamp || 0)
    );
  },

  // 格式化时间显示
  formatTimeAgo(timeString) {
    if (!timeString) return '未知时间';

    const now = new Date();
    const time = new Date(timeString);
    const diffMs = now - time;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 切换标签页
  onTabChange(e) {
    const activeTab = parseInt(e.currentTarget.dataset.index);

    // 切换到待处理标签页时，清空所有选择状态
    if (activeTab === 0) {
      // 重置待处理错题的选择状态
      const { pendingErrorsByCourse } = this.data;
      pendingErrorsByCourse.forEach(course => {
        course.allSelected = false;
        course.words.forEach(word => {
          word.selected = false;
        });
      });

      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false,
        pendingErrorsByCourse
      });
    } else {
      // 切换到历史记录标签页时，只需要清空选择状态，不需要操作课程数据
      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false
      });
    }

    console.log(`切换到标签页 ${activeTab}, 已清空选择状态`);
  },

  // 切换课程全选状态
  onToggleCourseSelect(e) {
    const { courseIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    const newSelectState = !course.allSelected;

    // 更新课程选择状态
    course.allSelected = newSelectState;
    course.words.forEach(word => {
      word.selected = newSelectState;
    });

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 切换单个字词选择状态
  onToggleWordSelect(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    if (wordIndex < 0 || wordIndex >= course.words.length) return;

    // 切换字词选择状态
    course.words[wordIndex].selected = !course.words[wordIndex].selected;

    // 更新课程全选状态
    const selectedCount = course.words.filter(word => word.selected).length;
    course.allSelected = selectedCount === course.words.length;

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 更新选中的字词列表
  updateSelectedWords() {
    const selectedWords = [];

    this.data.pendingErrorsByCourse.forEach(course => {
      course.words.forEach(word => {
        if (word.selected) {
          selectedWords.push(word);
        }
      });
    });

    // 更新全选状态
    const allSelected = this.data.totalPendingErrors > 0 &&
      selectedWords.length === this.data.totalPendingErrors;

    this.setData({
      selectedWords,
      allSelected
    });
  },

  // 全选/取消全选
  onToggleSelectAll() {
    const { pendingErrorsByCourse, allSelected } = this.data;
    const newSelectState = !allSelected;

    pendingErrorsByCourse.forEach(course => {
      course.allSelected = newSelectState;
      course.words.forEach(word => {
        word.selected = newSelectState;
      });
    });

    this.updateSelectedWords();
    this.setData({ pendingErrorsByCourse });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止按钮点击时触发字词选择
  },

  // 开始错题挑战
  onStartErrorChallenge() {
    const { selectedWords } = this.data;

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要练习的错题',
        icon: 'none'
      });
      return;
    }

    // 获取主要课程信息（选择最多错题的课程作为主课程）
    const courseGroups = {};
    selectedWords.forEach(error => {
      const courseId = error.courseInfo?.courseId || 'unknown';
      if (!courseGroups[courseId]) {
        courseGroups[courseId] = {
          courseInfo: error.courseInfo,
          count: 0
        };
      }
      courseGroups[courseId].count++;
    });

    // 找到错题最多的课程作为主课程
    const mainCourse = Object.values(courseGroups).reduce((max, current) =>
      current.count > max.count ? current : max
    );

    console.log('错题挑战 - 主课程信息:', mainCourse.courseInfo);
    console.log('错题挑战 - 选择的错题课程分布:', courseGroups);

    // 构建错题挑战数据，保留原有错题的课程信息
    const challengeWords = selectedWords.map(error => ({
      id: error.id,
      word: error.word,
      pinyin: error.pinyin,
      table: error.table || 'errorReview',
      originalCourseInfo: error.courseInfo // 保留原始课程信息用于订正时查找
    }));

    // 创建错题挑战会话 - 使用原有课程信息而不是创建新课程
    const challenge = {
      // 保持原有课程的核心信息
      courseId: mainCourse.courseInfo?.courseId || 'error_review',
      courseName: mainCourse.courseInfo?.courseName || '错题复习',
      courseTitle: mainCourse.courseInfo?.courseTitle || `错题练习 (${selectedWords.length}个字词)`,
      publisher: mainCourse.courseInfo?.publisher || '错题集',
      grade: mainCourse.courseInfo?.grade || '复习',
      term: mainCourse.courseInfo?.term || 'error',
      publisherId: mainCourse.courseInfo?.publisherId || 'error_review',
      gradeId: mainCourse.courseInfo?.gradeId || 'review',

      // 错题复习特有的信息
      selectedWords: challengeWords,
      timestamp: new Date().toISOString(),
      isErrorReview: true, // 标记为错题复习

      // 保存所有涉及的课程信息，用于多课程错题的处理
      involvedCourses: Object.keys(courseGroups).map(courseId => ({
        courseId,
        courseInfo: courseGroups[courseId].courseInfo,
        errorCount: courseGroups[courseId].count
      }))
    };

    console.log('错题挑战会话已创建:', challenge);

    // 保存挑战会话到全局数据
    app.globalData.currentChallenge = challenge;

    wx.showLoading({
      title: '准备错题挑战...'
    });

    // 跳转到练习页面
    setTimeout(() => {
      wx.hideLoading();

      wx.redirectTo({
        url: '/pages/practice/practice',
        success: () => {
          console.log('错题挑战开始，共选择', selectedWords.length, '个错题');
          console.log('涉及课程数量:', Object.keys(courseGroups).length);
        }
      });
    }, 1000);
  },

  // 标记错题为已订正
  onMarkAsCorreected(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showModal({
        title: '确认订正',
        content: `确定将"${word.word}"标记为已订正吗？`,
        success: (res) => {
          if (res.confirm) {
            this.markWordAsCorrected(word.id);
          }
        }
      });
    }
  },

  // 标记字词为已订正
  markWordAsCorrected(wordId) {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const wordIndex = errorWords.findIndex(error => error.id === wordId);

    if (wordIndex >= 0) {
      errorWords[wordIndex].corrected = true;
      errorWords[wordIndex].correctedTime = new Date().toISOString();

      wx.setStorageSync('errorWords', errorWords);

      wx.showToast({
        title: '已标记为订正',
        icon: 'success'
      });

      // 重新加载数据
      this.loadErrorWords();
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });

    setTimeout(() => {
      this.loadErrorWords();
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面分享
  onShareAppMessage() {
    const { totalPendingErrors } = this.data;
    return {
      title: `我的错题本有${totalPendingErrors}个待处理错题，一起来练习吧！`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-wrongbook.png'
    };
  },

  // 去练习
  onGoToPractice() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 调试错题数据
  debugErrorWords() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    console.log('调试：当前错题数据', errorWords);

    // 检查是否有问题数据
    const problemWords = errorWords.filter(error =>
      error.word === '暂无答案' || error.word === '未知字词' ||
      error.pinyin === '暂无拼音' || !error.word ||
      error.word === '无字词' || error.pinyin === '无拼音'
    );

    if (problemWords.length > 0) {
      console.log('发现问题数据:', problemWords);
      wx.showModal({
        title: '数据问题',
        content: `发现${problemWords.length}条错误数据，是否清除这些无效数据？`,
        success: (res) => {
          if (res.confirm) {
            this.cleanErrorData();
          }
        }
      });
    }
  },

  // 清理错误数据
  cleanErrorData() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const validWords = errorWords.filter(error =>
      error.word &&
      error.word !== '暂无答案' &&
      error.word !== '未知字词' &&
      error.word !== '无字词' &&
      error.pinyin !== '暂无拼音' &&
      error.pinyin !== '无拼音'
    );

    wx.setStorageSync('errorWords', validWords);
    wx.showToast({
      title: `已清除${errorWords.length - validWords.length}条无效数据`,
      icon: 'success',
      duration: 2000
    });

    // 重新加载数据
    this.loadErrorWords();
  },

  // 手动刷新数据
  onManualRefresh() {
    console.log('用户手动刷新错题集数据');
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 延迟一下再刷新，确保loading显示
    setTimeout(() => {
      this.loadErrorWords();
      wx.hideLoading();

      wx.showToast({
        title: '数据已刷新',
        icon: 'success',
        duration: 1000
      });
    }, 500);
  },

  // 长按头部统计区域的调试功能
  onLongPressHeader() {
    wx.showActionSheet({
      itemList: ['清除所有错题', '重新加载数据', '查看错题详情', '查看存储数据'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.clearAllErrors();
        } else if (res.tapIndex === 1) {
          this.onManualRefresh();
        } else if (res.tapIndex === 2) {
          this.showErrorDetails();
        } else if (res.tapIndex === 3) {
          this.showStorageData();
        }
      }
    });
  },

  // 显示错题详情
  showErrorDetails() {
    const errorWords = wx.getStorageSync('errorWords') || [];

    let details = `错题总数: ${errorWords.length}\n\n`;

    errorWords.forEach((error, index) => {
      details += `${index + 1}. ${error.word}\n`;
      details += `   课程: ${error.courseInfo?.courseName || '未知'}\n`;
      details += `   状态: ${error.corrected ? '已订正' : '未订正'}\n`;
      if (error.correctedTime) {
        details += `   订正时间: ${new Date(error.correctedTime).toLocaleString()}\n`;
      }
      details += '\n';
    });

    wx.showModal({
      title: '错题详情',
      content: details,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示存储数据
  showStorageData() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const cachedResults = wx.getStorageSync('cachedPracticeResults');

    let content = `错题数据:\n总数: ${errorWords.length}\n`;
    content += `已订正: ${errorWords.filter(e => e.corrected).length}\n`;
    content += `未订正: ${errorWords.filter(e => !e.corrected).length}\n\n`;

    if (cachedResults) {
      content += `缓存结果:\n${JSON.stringify(cachedResults, null, 2)}`;
    } else {
      content += '无缓存结果';
    }

    console.log('存储数据详情:', { errorWords, cachedResults });

    wx.showModal({
      title: '存储数据',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 清除所有错题数据
  clearAllErrors() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有错题数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          wx.setStorageSync('errorWords', []);
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          });
          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 长按字词项显示操作菜单
  onLongPressWord(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showActionSheet({
        itemList: ['标记为已订正', '删除错题'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 标记为已订正
            this.markWordAsCorrected(word.id);
          } else if (res.tapIndex === 1) {
            // 删除错题
            this.deleteErrorWord(word.id);
          }
        }
      });
    }
  },

  // 删除错题
  deleteErrorWord(wordId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个错题吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          const errorWords = wx.getStorageSync('errorWords') || [];
          const filteredWords = errorWords.filter(error => error.id !== wordId);

          wx.setStorageSync('errorWords', filteredWords);

          wx.showToast({
            title: '已删除',
            icon: 'success'
          });

          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 检查是否有错题复习结果
  checkErrorReviewResult() {
    const cachedResults = wx.getStorageSync('cachedPracticeResults');
    if (cachedResults && cachedResults.isErrorReview) {
      console.log('检测到错题复习完成，缓存结果:', cachedResults);

      // 清除缓存的练习结果
      wx.removeStorageSync('cachedPracticeResults');

      let message = '错题状态已更新';
      if (cachedResults.detailedLog) {
        message = cachedResults.detailedLog;
      } else if (cachedResults.correctedCount > 0) {
        message = `已订正 ${cachedResults.correctedCount} 个错题`;
      }

      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
    } else {
      console.log('没有检测到错题复习缓存结果');
    }
  },

  // 绘制田字格
  drawGrid() {
    console.log('开始绘制田字格');
    
    // 延迟绘制，确保DOM渲染完成
    setTimeout(() => {
      try {
        // 使用兼容性更好的Canvas API
        const query = wx.createSelectorQuery().in(this);
        query.select('.grid-canvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            console.log('Canvas查询结果:', res);
            if (res && res[0] && res[0].node) {
              // Canvas 2D API (新版本)
              const canvas = res[0].node;
              const ctx = canvas.getContext('2d');
              
              console.log('使用Canvas 2D API');
              
              // 设置Canvas尺寸
              const dpr = wx.getSystemInfoSync().pixelRatio || 1;
              canvas.width = 300 * dpr;
              canvas.height = 300 * dpr;
              ctx.scale(dpr, dpr);
              
              this.drawGridLines(ctx);
              console.log('田字格绘制完成(新API)');
            } else {
              console.log('Canvas 2D API不可用，降级到旧版API');
              // 降级到旧版API
              this.drawGridOldAPI();
            }
          });
      } catch (error) {
        console.error('绘制田字格失败，尝试旧版API:', error);
        this.drawGridOldAPI();
      }
    }, 200);
  },

  // 旧版Canvas API绘制田字格
  drawGridOldAPI() {
    try {
      console.log('使用旧版Canvas API绘制田字格');
      const ctx = wx.createCanvasContext('gridCanvas', this);
      
      if (!ctx) {
        console.error('无法创建Canvas上下文');
        return;
      }
      
      // 先清除画布
      ctx.clearRect(0, 0, 300, 300);
      
      this.drawGridLines(ctx);
      
      // 使用draw方法提交绘制
      ctx.draw(false, () => {
        console.log('田字格绘制完成(旧API) - 绘制回调执行');
      });
    } catch (error) {
      console.error('旧版API绘制田字格失败:', error);
      
      // 最后的备用方案：使用基础绘制
      setTimeout(() => {
        this.drawBasicGrid();
      }, 100);
    }
  },

  // 基础田字格绘制方法
  drawBasicGrid() {
    try {
      console.log('使用基础方法绘制田字格');
      const ctx = wx.createCanvasContext('gridCanvas', this);
      
      if (!ctx) {
        console.error('无法创建Canvas上下文');
        return;
      }
      
      // 先清除画布
      ctx.clearActions();
      
      // 设置背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 300, 300);
      
      // 绘制外框
      ctx.setStrokeStyle('#333333');
      ctx.setLineWidth(1);
      ctx.strokeRect(10, 10, 280, 280);
      
      // 绘制十字线
      ctx.setStrokeStyle('#999999');
      ctx.setLineWidth(1);
      
      // 垂直中线
      ctx.beginPath();
      ctx.moveTo(150, 10);
      ctx.lineTo(150, 290);
      ctx.stroke();
      
      // 水平中线
      ctx.beginPath();
      ctx.moveTo(10, 150);
      ctx.lineTo(290, 150);
      ctx.stroke();
      
      // 对角线
      ctx.setStrokeStyle('#cccccc');
      ctx.beginPath();
      ctx.moveTo(10, 10);
      ctx.lineTo(290, 290);
      ctx.moveTo(290, 10);
      ctx.lineTo(10, 290);
      ctx.stroke();
      
      // 调用draw方法提交绘制，false表示先清除画布再绘制
      ctx.draw(false, () => {
        console.log('基础田字格绘制完成-回调函数');
      });
      console.log('基础田字格绘制指令已发送');
    } catch (error) {
      console.error('基础田字格绘制失败:', error);
    }
  },

  // 绘制田字格线条
  drawGridLines(ctx) {
    const canvasSize = 300;
    const padding = 10; // 边距
    const gridSize = canvasSize - (padding * 2); // 田字格实际尺寸
    const startX = padding;
    const startY = padding;
    
    console.log('开始绘制田字格线条:', { canvasSize, gridSize, startX, startY });
    
    // 检测是新版API还是旧版API
    const isNewAPI = typeof ctx.clearRect !== 'undefined';
    
    if (isNewAPI) {
      // 新版Canvas 2D API
      // 清除画布
      ctx.clearRect(0, 0, canvasSize, canvasSize);
      
      // 设置背景
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvasSize, canvasSize);
      
      // 设置外框线条样式
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 2;
      
      // 绘制外框
      ctx.strokeRect(startX, startY, gridSize, gridSize);
      
      // 设置内部线条样式
      ctx.strokeStyle = '#999';
      ctx.lineWidth = 1;
      
      // 绘制十字交叉线
      const centerX = startX + gridSize / 2;
      const centerY = startY + gridSize / 2;
      
      ctx.beginPath();
      // 垂直中线
      ctx.moveTo(centerX, startY);
      ctx.lineTo(centerX, startY + gridSize);
      // 水平中线
      ctx.moveTo(startX, centerY);
      ctx.lineTo(startX + gridSize, centerY);
      ctx.stroke();
      
      // 绘制对角线（虚线）
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.setLineDash([8, 4]);
      
      ctx.beginPath();
      // 左上到右下对角线
      ctx.moveTo(startX, startY);
      ctx.lineTo(startX + gridSize, startY + gridSize);
      // 右上到左下对角线
      ctx.moveTo(startX + gridSize, startY);
      ctx.lineTo(startX, startY + gridSize);
      ctx.stroke();
      
      // 恢复实线
      ctx.setLineDash([]);
      
    } else {
      // 旧版微信小程序Canvas API
      // 清除画布
      ctx.clearRect(0, 0, canvasSize, canvasSize);
      
      // 设置背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, canvasSize, canvasSize);
      
      // 设置外框线条样式
      ctx.setStrokeStyle('#333');
      ctx.setLineWidth(2);
      
      // 绘制外框
      ctx.strokeRect(startX, startY, gridSize, gridSize);
      
      // 设置内部线条样式
      ctx.setStrokeStyle('#999');
      ctx.setLineWidth(1);
      
      // 绘制十字交叉线
      const centerX = startX + gridSize / 2;
      const centerY = startY + gridSize / 2;
      
      ctx.beginPath();
      // 垂直中线
      ctx.moveTo(centerX, startY);
      ctx.lineTo(centerX, startY + gridSize);
      // 水平中线
      ctx.moveTo(startX, centerY);
      ctx.lineTo(startX + gridSize, centerY);
      ctx.stroke();
      
      // 绘制对角线（虚线）
      ctx.setStrokeStyle('#ccc');
      ctx.setLineWidth(1);
      ctx.setLineDash([8, 4]);
      
      ctx.beginPath();
      // 左上到右下对角线
      ctx.moveTo(startX, startY);
      ctx.lineTo(startX + gridSize, startY + gridSize);
      // 右上到左下对角线
      ctx.moveTo(startX + gridSize, startY);
      ctx.lineTo(startX, startY + gridSize);
      ctx.stroke();
      
      // 恢复实线
      ctx.setLineDash([]);
    }
    
    console.log('田字格线条绘制完成');
  },

  // 强制重绘田字格
  forceRedrawGrid() {
    console.log('强制重绘田字格');
    if (this.data.showStrokeModal) {
      this.checkCanvasExists();
      setTimeout(() => {
        this.drawBasicGrid();
      }, 100);
      setTimeout(() => {
        this.drawGridOldAPI();
      }, 300);
    }
  },

  // 此处移除了测试绘制田字格的方法

  // 智能分割拼音字符串
  splitPinyinString(pinyinString, wordLength) {
    // 如果已经有空格分隔，直接分割
    if (pinyinString.includes(' ')) {
      const parts = pinyinString.trim().split(/\s+/);
      if (parts.length === wordLength) {
        return parts;
      }
    }

    // 对于连续的拼音字符串，尝试智能分割
    if (wordLength === 1) {
      return [pinyinString];
    }

    // 简单的拼音分割规则：按声母韵母分割
    // 这是一个简化版本，实际可能需要更复杂的拼音库
    const pinyinRegex = /[bpmfdtnlgkhjqxzcsrwy]*[aeiouv]+[ng]?[1-4]?/gi;
    const matches = pinyinString.match(pinyinRegex) || [];

    if (matches.length === wordLength) {
      return matches;
    }

    // 如果无法智能分割，尝试平均分割
    if (wordLength > 1) {
      const avgLength = Math.floor(pinyinString.length / wordLength);
      const parts = [];
      for (let i = 0; i < wordLength; i++) {
        const start = i * avgLength;
        const end = i === wordLength - 1 ? pinyinString.length : (i + 1) * avgLength;
        parts.push(pinyinString.substring(start, end));
      }
      return parts;
    }

    // 默认情况：第一个字符显示完整拼音，其他为空
    const result = Array(wordLength).fill('');
    result[0] = pinyinString;
    return result;
  },

  // 点击历史记录字词显示笔画动画
  onShowStrokeAnimation(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { historyErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < historyErrorsByCourse.length &&
        wordIndex >= 0 && wordIndex < historyErrorsByCourse[courseIndex].words.length) {

      const word = historyErrorsByCourse[courseIndex].words[wordIndex];
      console.log('点击字词显示笔画动画:', word.word);

      // 设置当前字词信息
      this.setData({
        showStrokeModal: true,
        currentWord: word.word,
        currentCharacter: word.word.charAt(0), // 默认显示第一个字符
        currentCharacterIndex: 0,
        isAutoPlaying: word.word.length > 1 // 如果是多字词，启用自动播放
      });

      // 使用wx.nextTick确保DOM完全渲染后再初始化
      wx.nextTick(() => {
        // 给足够时间让DOM渲染完成
        setTimeout(() => {
          console.log('绘制田字格和初始化汉字书写器');
          // 直接使用基础绘制方法
          this.drawGridOldAPI();
          // 延迟初始化汉字书写器，确保Canvas先绘制
          setTimeout(() => {
            this.initHanziWriter();
          }, 200);
        }, 300);
      });
    }
  },

  // 初始化汉字书写器
  initHanziWriter() {
    const { currentCharacter, showStrokeModal } = this.data;

    if (!currentCharacter || !showStrokeModal) {
      console.warn('没有要显示的字符或弹窗已关闭');
      return;
    }

    // 防止重复初始化
    if (this._isInitializing) {
      console.log('正在初始化中，跳过重复调用');
      return;
    }

    this._isInitializing = true;

    // 确保在初始化前清理之前的实例
    this.cleanupWriterInstance();

    console.log('初始化汉字书写器，字符:', currentCharacter);

    try {
      // 检查组件是否存在
      const writerComponent = this.selectComponent('#hz-writer');
      if (!writerComponent) {
        console.error('无法找到汉字书写器组件');
        this._isInitializing = false;
        return;
      }

      // 使用正确的方式创建汉字书写器实例
      this._writerCtx = createHanziWriterContext({
        id: 'hz-writer',
        page: this,
        character: currentCharacter,
        width: 300,
        height: 300,
        padding: 10,
        strokeColor: '#333',
        strokeAnimationSpeed: 1,
        delayBetweenStrokes: 800,
        onLoadCharDataSuccess: () => {
          console.log('字符数据加载成功:', currentCharacter);
          this._isInitializing = false;
          // 延迟启动动画，确保渲染完成
          setTimeout(() => {
            this.startCharacterAnimation();
          }, 200);
        },
        onLoadCharDataError: (error) => {
          console.error('字符数据加载失败:', error);
          this._isInitializing = false;
        }
      });

    } catch (error) {
      console.error('初始化汉字书写器失败:', error);
      this._isInitializing = false;
    }
  },

  // 开始字符动画
  startCharacterAnimation() {
    if (!this._writerCtx || !this.data.showStrokeModal) {
      return;
    }

    try {
      if (this.data.isAutoPlaying && this.data.currentWord.length > 1) {
        // 多字自动播放模式：播放一次动画后自动切换到下一个字
        this._writerCtx.animateCharacter({
          onComplete: () => {
            // 动画完成后，延迟切换到下一个字符
            setTimeout(() => {
              this.switchToNextCharacter();
            }, 1500); // 1.5秒后切换
          }
        });
      } else {
        // 单字或手动模式：循环播放
        this._writerCtx.loopCharacterAnimation();
      }
    } catch (error) {
      console.error('播放动画失败:', error);
      // 备用方案
      try {
        this._writerCtx.animateCharacter();
      } catch (e2) {
        console.error('备用动画方法也失败:', e2);
      }
    }
  },

  // 切换到下一个字符
  switchToNextCharacter() {
    const { currentWord, currentCharacterIndex, showStrokeModal } = this.data;
    
    if (!showStrokeModal || currentWord.length <= 1) {
      return;
    }

    const nextIndex = (currentCharacterIndex + 1) % currentWord.length;
    const nextCharacter = currentWord.charAt(nextIndex);

    this.setData({
      currentCharacter: nextCharacter,
      currentCharacterIndex: nextIndex
    });

    // 先清理现有实例
    this.cleanupWriterInstance();
    
    // 重新绘制田字格和初始化汉字书写器
    setTimeout(() => {
      console.log('切换字符时重新绘制田字格');
      this.drawGridOldAPI(); // 使用旧版API绘制田字格
      // 延迟初始化汉字书写器
      setTimeout(() => {
        this.initHanziWriter();
      }, 200);
    }, 100);
  },

  // 切换字符显示
  onSwitchCharacter(e) {
    const { index } = e.currentTarget.dataset;
    const { currentWord } = this.data;

    if (index >= 0 && index < currentWord.length) {
      const newCharacter = currentWord.charAt(index);

      this.setData({
        currentCharacter: newCharacter,
        currentCharacterIndex: index,
        isAutoPlaying: false // 手动切换时关闭自动播放
      });

      // 先清理现有实例
      this.cleanupWriterInstance();
      
      // 先绘制田字格，再初始化汉字书写器
      setTimeout(() => {
        this.drawGridOldAPI();
        setTimeout(() => {
          this.initHanziWriter();
        }, 200);
      }, 100);
    }
  },

  // 关闭笔画动画弹窗
  onCloseStrokeModal() {
    // 停止自动播放
    if (this.autoPlayTimer) {
      clearTimeout(this.autoPlayTimer);
      this.autoPlayTimer = null;
    }

    // 使用安全清理方法
    this.cleanupWriterInstance();
    
    // 清理初始化标志
    this._isInitializing = false;

    // 安全清理Canvas上下文
    try {
      const ctx = wx.createCanvasContext('gridCanvas', this);
      if (ctx) {
        ctx.draw(true); // 使用空白的draw指令清空画布
        ctx.draw();
      }
    } catch (e) {
      console.warn('清理Canvas失败:', e);
    }

    // 重置初始化标志
    this._isInitializing = false;

    this.setData({
      showStrokeModal: false,
      currentCharacter: '',
      currentCharacterIndex: 0,
      currentWord: '',
      isAutoPlaying: false
    });
  },

  // Canvas存在性检测
  checkCanvasExists() {
    console.log('检查Canvas是否存在');
    
    const query = wx.createSelectorQuery().in(this);
    query.select('.grid-canvas')
      .boundingClientRect()
      .exec((res) => {
        console.log('Canvas元素查询结果:', res);
        if (res && res[0]) {
          console.log('Canvas元素存在，位置:', res[0]);
          console.log('Canvas尺寸:', res[0].width, 'x', res[0].height);
        } else {
          console.error('Canvas元素不存在或不可见');
        }
      });
    
    // 同时检查canvas-id
    const canvasCtx = wx.createCanvasContext('gridCanvas', this);
    console.log('Canvas上下文创建结果:', canvasCtx);
  },

  // 安全清理汉字书写器实例
  cleanupWriterInstance() {
    console.log('安全清理汉字书写器实例');
    
    // 清理之前的实例
    if (this._writerCtx) {
      try {
        // 先取消所有动画
        if (typeof this._writerCtx.cancelAnimation === 'function') {
          this._writerCtx.cancelAnimation();
        }
        
        // 安全延迟后再销毁
        setTimeout(() => {
          try {
            if (this._writerCtx && typeof this._writerCtx.destroy === 'function') {
              this._writerCtx.destroy();
            }
          } catch (e) {
            console.warn('延迟销毁汉字书写器实例失败:', e);
          }
          this._writerCtx = null;
        }, 100);
      } catch (e) {
        console.warn('清理汉字书写器实例失败:', e);
        this._writerCtx = null;
      }
    }
  },

  // ...existing code...
});
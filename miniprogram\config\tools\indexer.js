/**
 * 教材配置索引工具
 * 用于处理和管理教材配置文件
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_DIR = path.join(__dirname, '..');
const TEXTBOOKS_INDEX = path.join(CONFIG_DIR, 'textbooks.json');

/**
 * 读取索引文件
 * @returns {Object} 教材索引对象
 */
function readIndex() {
  try {
    const data = fs.readFileSync(TEXTBOOKS_INDEX, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('读取索引文件失败:', error);
    return { versions: [] };
  }
}

/**
 * 读取特定版本的教材内容
 * @param {string} versionId 版本ID
 * @returns {Object|null} 教材内容对象
 */
function readTextbook(versionId) {
  const index = readIndex();
  const version = index.versions.find(v => v.id === versionId);
  
  if (!version || !version.file) {
    console.error(`未找到版本: ${versionId}`);
    return null;
  }
  
  try {
    const filePath = path.join(CONFIG_DIR, version.file);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`读取教材内容失败: ${versionId}`, error);
    return null;
  }
}

/**
 * 查找特定课程的词表
 * @param {string} versionId 版本ID
 * @param {string} gradeId 年级ID
 * @param {string} termId 学期ID
 * @param {string} lessonId 课程ID
 * @param {string} tableType 表格类型 (shiZiBiao/xieZiBiao/ciYuBiao)
 * @returns {Array|null} 词表数组
 */
function findTable(versionId, gradeId, termId, lessonId, tableType) {
  const textbook = readTextbook(versionId);
  if (!textbook) return null;
  
  try {
    const grade = textbook.grades[gradeId];
    const term = grade.terms[termId];
    const lesson = term.lessons.find(l => l.id === lessonId);
    return lesson.tables[tableType] || null;
  } catch (error) {
    console.error('查找词表失败:', error);
    return null;
  }
}

/**
 * 获取所有版本列表
 * @returns {Array} 版本对象数组
 */
function getAllVersions() {
  const index = readIndex();
  return index.versions || [];
}

/**
 * 获取指定版本的所有年级
 * @param {string} versionId 版本ID
 * @returns {Array} 年级对象数组
 */
function getGrades(versionId) {
  const index = readIndex();
  const version = index.versions.find(v => v.id === versionId);
  return version ? version.grades : [];
}

/**
 * 获取指定版本和年级的所有学期
 * @param {string} versionId 版本ID
 * @param {string} gradeId 年级ID
 * @returns {Array} 学期对象数组
 */
function getTerms(versionId, gradeId) {
  const grades = getGrades(versionId);
  const grade = grades.find(g => g.id === gradeId);
  return grade ? grade.terms : [];
}

/**
 * 获取指定版本、年级和学期的所有课程
 * @param {string} versionId 版本ID
 * @param {string} gradeId 年级ID
 * @param {string} termId 学期ID
 * @returns {Array} 课程对象数组
 */
function getLessons(versionId, gradeId, termId) {
  const textbook = readTextbook(versionId);
  if (!textbook) return [];
  
  try {
    const grade = textbook.grades[gradeId];
    const term = grade.terms[termId];
    return term.lessons || [];
  } catch (error) {
    console.error('获取课程列表失败:', error);
    return [];
  }
}

/**
 * 统计数据
 * @returns {Object} 统计信息
 */
function getStatistics() {
  const index = readIndex();
  const stats = {
    versions: 0,
    grades: 0,
    terms: 0,
    lessons: 0,
    words: {
      shiZiBiao: 0,
      xieZiBiao: 0,
      ciYuBiao: 0
    }
  };
  
  // 统计版本数
  stats.versions = index.versions.length;
  
  // 遍历每个版本统计详情
  for (const version of index.versions) {
    const textbook = readTextbook(version.id);
    if (!textbook) continue;
    
    // 统计年级数
    const gradesCount = Object.keys(textbook.grades).length;
    stats.grades += gradesCount;
    
    // 遍历每个年级
    for (const gradeId in textbook.grades) {
      const grade = textbook.grades[gradeId];
      
      // 统计学期数
      const termsCount = Object.keys(grade.terms).length;
      stats.terms += termsCount;
      
      // 遍历每个学期
      for (const termId in grade.terms) {
        const term = grade.terms[termId];
        
        // 统计课程数
        const lessonsCount = term.lessons.length;
        stats.lessons += lessonsCount;
        
        // 遍历每个课程统计词表
        for (const lesson of term.lessons) {
          if (lesson.tables) {
            if (lesson.tables.shiZiBiao) {
              stats.words.shiZiBiao += lesson.tables.shiZiBiao.length;
            }
            if (lesson.tables.xieZiBiao) {
              stats.words.xieZiBiao += lesson.tables.xieZiBiao.length;
            }
            if (lesson.tables.ciYuBiao) {
              stats.words.ciYuBiao += lesson.tables.ciYuBiao.length;
            }
          }
        }
      }
    }
  }
  
  return stats;
}

/**
 * 导出特定课程的词表为单独的JSON文件
 * @param {string} versionId 版本ID
 * @param {string} gradeId 年级ID
 * @param {string} termId 学期ID
 * @param {string} lessonId 课程ID
 * @param {string} tableType 表格类型 (shiZiBiao/xieZiBiao/ciYuBiao)
 * @returns {boolean} 是否成功导出
 */
function exportTable(versionId, gradeId, termId, lessonId, tableType) {
  const table = findTable(versionId, gradeId, termId, lessonId, tableType);
  if (!table) return false;
  
  try {
    const exportDir = path.join(CONFIG_DIR, 'exports');
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir);
    }
    
    const fileName = `${versionId}_${gradeId}_${termId}_${lessonId}_${tableType}.json`;
    const filePath = path.join(exportDir, fileName);
    
    fs.writeFileSync(filePath, JSON.stringify(table, null, 2), 'utf8');
    console.log(`成功导出到: ${filePath}`);
    return true;
  } catch (error) {
    console.error('导出词表失败:', error);
    return false;
  }
}

/**
 * 搜索字词
 * @param {string} query 搜索关键词
 * @returns {Array} 搜索结果数组
 */
function searchWords(query) {
  if (!query || query.trim() === '') {
    return [];
  }
  
  const results = [];
  const index = readIndex();
  
  // 遍历每个版本
  for (const version of index.versions) {
    const textbook = readTextbook(version.id);
    if (!textbook) continue;
    
    // 遍历每个年级
    for (const gradeId in textbook.grades) {
      const grade = textbook.grades[gradeId];
      
      // 遍历每个学期
      for (const termId in grade.terms) {
        const term = grade.terms[termId];
        
        // 遍历每个课程
        for (const lesson of term.lessons) {
          if (!lesson.tables) continue;
          
          // 在识字表中搜索
          if (lesson.tables.shiZiBiao) {
            for (const item of lesson.tables.shiZiBiao) {
              if (item.word.includes(query) || item.pinyin.includes(query)) {
                results.push({
                  word: item.word,
                  pinyin: item.pinyin,
                  type: '识字表',
                  version: version.name,
                  grade: grade.name,
                  term: term.name,
                  lesson: lesson.name,
                  title: lesson.title,
                  path: `${version.id}.${gradeId}.${termId}.${lesson.id}.shiZiBiao`
                });
              }
            }
          }
          
          // 在写字表中搜索
          if (lesson.tables.xieZiBiao) {
            for (const item of lesson.tables.xieZiBiao) {
              if (item.word.includes(query) || item.pinyin.includes(query)) {
                results.push({
                  word: item.word,
                  pinyin: item.pinyin,
                  type: '写字表',
                  version: version.name,
                  grade: grade.name,
                  term: term.name,
                  lesson: lesson.name,
                  title: lesson.title,
                  path: `${version.id}.${gradeId}.${termId}.${lesson.id}.xieZiBiao`
                });
              }
            }
          }
          
          // 在词语表中搜索
          if (lesson.tables.ciYuBiao) {
            for (const item of lesson.tables.ciYuBiao) {
              if (item.word.includes(query) || item.pinyin.includes(query)) {
                results.push({
                  word: item.word,
                  pinyin: item.pinyin,
                  type: '词语表',
                  version: version.name,
                  grade: grade.name,
                  term: term.name,
                  lesson: lesson.name,
                  title: lesson.title,
                  path: `${version.id}.${gradeId}.${termId}.${lesson.id}.ciYuBiao`
                });
              }
            }
          }
        }
      }
    }
  }
  
  return results;
}

// 示例用法
function runExample() {
  console.log('教材统计信息:');
  console.log(getStatistics());
  
  console.log('\n所有版本:');
  console.log(getAllVersions());
  
  const versionId = 'renjiaoban';
  console.log(`\n${versionId} 的所有年级:`);
  console.log(getGrades(versionId));
  
  const gradeId = 'grade1';
  console.log(`\n${versionId}.${gradeId} 的所有学期:`);
  console.log(getTerms(versionId, gradeId));
  
  const termId = 'term1';
  console.log(`\n${versionId}.${gradeId}.${termId} 的所有课程:`);
  console.log(getLessons(versionId, gradeId, termId));
  
  const lessonId = 'lesson1';
  const tableType = 'shiZiBiao';
  console.log(`\n${versionId}.${gradeId}.${termId}.${lessonId}.${tableType} 的内容:`);
  console.log(findTable(versionId, gradeId, termId, lessonId, tableType));
  
  console.log('\n搜索结果 "天":');
  console.log(searchWords('天'));
}

// 导出所有函数
module.exports = {
  readIndex,
  readTextbook,
  findTable,
  getAllVersions,
  getGrades,
  getTerms,
  getLessons,
  getStatistics,
  exportTable,
  searchWords,
  runExample
};

// 如果直接运行此脚本，则执行示例
if (require.main === module) {
  runExample();
} 
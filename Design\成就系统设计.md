# 成就系统设计文档

## 1. 功能描述

成就系统是小学字词听写软件的激励模块，通过积分体系、成就徽章和排行榜等游戏化设计，激发学生学习兴趣，提升学习积极性。该系统将学习行为量化为可视化的成就和奖励，让学习过程更加有趣和富有挑战性。

### 1.1 核心功能
- **积分体系**：根据学习行为获得积分，用于解锁内容和兑换奖励
- **成就徽章**：完成特定挑战获得徽章，展示学习成果
- **排行榜**：与同学、朋友比较学习进度，增强竞争性
- **等级系统**：通过积分累积提升用户等级

### 1.2 功能目标
- 激发学习兴趣，让学习变得有趣
- 培养学习习惯，鼓励持续学习
- 增强成就感，提升自信心
- 促进良性竞争，共同进步

## 2. 功能实现流程

### 2.1 积分获得流程
```mermaid
graph TD
    A[用户完成学习行为] --> B[系统识别行为类型]
    B --> C[计算积分奖励]
    C --> D[检查连击加成]
    D --> E[应用难度系数]
    E --> F[更新用户积分]
    F --> G[检查等级提升]
    G -->|等级提升| H[推送等级提升通知]
    G -->|无提升| I[更新积分显示]
    H --> I
    I --> J[记录积分日志]
```

### 2.2 成就徽章获得流程
```mermaid
graph TD
    A[监听用户行为] --> B[更新成就进度]
    B --> C{是否达成条件}
    C -->|否| D[保存进度数据]
    C -->|是| E[触发成就获得]
    E --> F[发放徽章奖励]
    F --> G[播放获得动画]
    G --> H[推送通知消息]
    H --> I[更新成就展示]
    D --> J[更新进度条]
    I --> K[记录成就日志]
    J --> K
```

### 2.3 排行榜更新流程
```mermaid
graph TD
    A[用户积分变化] --> B[更新排行榜缓存]
    B --> C[计算排名变化]
    C --> D{排名是否变化}
    D -->|是| E[推送排名变化通知]
    D -->|否| F[更新排行榜显示]
    E --> F
    F --> G[更新好友排行]
    G --> H[更新班级排行]
    H --> I[更新全校排行]
```

## 3. 业务规则

### 3.1 积分体系规则
- **基础积分**：
  - 完成练习：10积分/题
  - 全部正确：额外20积分
  - 连续正确：每连续5题额外10积分
  - 首次完成单元：50积分
- **时间奖励**：
  - 连续学习天数：每天5积分
  - 连续7天：额外50积分
  - 连续30天：额外200积分
- **难度系数**：
  - 一年级：1.0倍
  - 二年级：1.1倍
  - 三年级：1.2倍
  - 四年级：1.3倍
  - 五年级：1.4倍
  - 六年级：1.5倍

### 3.2 等级系统规则
```
等级1: 0-99积分 (小学徒)
等级2: 100-299积分 (小书童)
等级3: 300-599积分 (小学者)
等级4: 600-999积分 (小博士)
等级5: 1000-1499积分 (小专家)
等级6: 1500-2199积分 (小大师)
等级7: 2200-2999积分 (小宗师)
等级8: 3000-3999积分 (小圣贤)
等级9: 4000-5499积分 (小天才)
等级10: 5500+积分 (小神童)
```

### 3.3 成就徽章分类
- **学习类徽章**：
  - 勤奋学者：连续学习7天
  - 坚持不懈：连续学习30天
  - 学习达人：累计学习100天
- **准确类徽章**：
  - 神射手：单次练习100%正确率
  - 完美主义：连续5次100%正确率
  - 精准大师：正确率保持95%以上
- **挑战类徽章**：
  - 速度之王：单题平均用时少于3秒
  - 马拉松选手：单次练习超过100题
  - 全能选手：完成所有年级内容

### 3.4 排行榜规则
- **排行榜类型**：
  - 好友排行：微信好友中使用应用的用户
  - 班级排行：同年级同班级用户
  - 年级排行：同年级所有用户
  - 全校排行：同学校所有用户
- **排名计算**：
  - 主要依据：当周积分增量
  - 次要依据：总积分数量
  - 更新频率：实时更新，每小时刷新缓存
- **隐私保护**：
  - 仅显示昵称，不显示真实姓名
  - 用户可选择退出排行榜
  - 不显示具体积分数，仅显示排名

## 4. 界面设计要求

### 4.1 成就中心页面
```
┌─────────────────────────────────────┐
│  ←    成就中心                      │
├─────────────────────────────────────┤
│                                   │
│  ┌─ 我的等级 ──────────────────┐    │
│  │    [🎓] 等级5 小专家          │    │
│  │  ████████░░ 1260/1500       │    │
│  │     距离下一级还需240积分      │    │
│  └─────────────────────────────┘    │
│                                   │
│  ┌─ 本周积分 ──────────────────┐    │
│  │          +186               │    │
│  │    ┌─┬─┬─┬─┬─┬─┬─┐         │    │
│  │    │▉││▊││▋││││││││          │    │
│  │    └─┴─┴─┴─┴─┴─┴─┘         │    │
│  │   一 二 三 四 五 六 日        │    │
│  └─────────────────────────────┘    │
│                                   │
│  ┌─ 最近获得 ──────────────────┐    │
│  │  [🏆] 勤奋学者 2024.03.15     │    │
│  │  [⭐] 神射手   2024.03.14     │    │
│  │  [🎯] 完美主义 2024.03.12     │    │
│  └─────────────────────────────┘    │
│                                   │
│    查看全部徽章    查看排行榜       │
└─────────────────────────────────────┘
```

### 4.2 徽章展示页面
```
┌─────────────────────────────────────┐
│  ←    我的徽章                      │
├─────────────────────────────────────┤
│                                   │
│  学习类徽章 (3/6)                   │
│  ┌───┬───┬───┬───┬───┬───┐    │
│  │🏆 ││⭐ ││📚 ││🔒 ││🔒 ││🔒 │    │
│  │勤奋││每日││学习││坚持││学习││百││    │
│  │学者││之星││达人││不懈││狂人││科│    │
│  └───┴───┴───┴───┴───┴───┘    │
│                                   │
│  准确类徽章 (2/5)                   │
│  ┌───┬───┬───┬───┬───┐       │
│  │🎯 ││💯 ││🔒 ││🔒 ││🔒 │       │
│  │神射││完美││精准││命中││百发│       │
│  │手 ││主义││大师││率王││百中│       │
│  └───┴───┴───┴───┴───┘       │
│                                   │
│  挑战类徽章 (1/4)                   │
│  ┌───┬───┬───┬───┐           │
│  │⚡ ││🔒 ││🔒 ││🔒 │           │
│  │速度││马拉││全能││极限│           │
│  │之王││松手││选手││挑战│           │
│  └───┴───┴───┴───┘           │
│                                   │
│      已获得6个徽章，还有9个待解锁     │
└─────────────────────────────────────┘
```

### 4.3 排行榜页面
```
┌─────────────────────────────────────┐
│  ←    排行榜                        │
├─────────────────────────────────────┤
│                                   │
│  ┌─ 好友排行 ─┬─ 班级排行 ─┬─ 年级排行 ┐│
│  │     ▼     │         │         ││
│  └───────────┴─────────┴─────────┘│
│                                   │
│  本周积分排行 (3月11日-3月17日)     │
│                                   │
│  ┌─────────────────────────────┐    │
│  │  🥇  小红同学      +298分     │    │
│  │  🥈  小明同学      +267分     │    │
│  │  🥉  小李同学      +234分     │    │
│  │   4  小王同学      +198分     │    │
│  │   5  小张同学      +186分 ←我  │    │
│  │   6  小陈同学      +165分     │    │
│  │   7  小刘同学      +142分     │    │
│  │   8  小杨同学      +128分     │    │
│  │   9  小周同学      +115分     │    │
│  │  10  小吴同学      +98分      │    │
│  └─────────────────────────────┘    │
│                                   │
│         我的排名：第5名             │
│        再努力12分就能升到第4名！     │
└─────────────────────────────────────┘
```

### 4.4 积分获得动画
```
┌─────────────────────────────────────┐
│                                   │
│                                   │
│           ✨ 太棒了！ ✨           │
│                                   │
│              +20 积分              │
│                                   │
│         ┌─ 获得原因 ──────┐         │
│         │ • 全部答对 +20  │         │
│         │ • 连击奖励 +10  │         │
│         │ • 速度奖励 +5   │         │
│         └─────────────────┘         │
│                                   │
│           当前积分：1285            │
│         距离升级还需215积分          │
│                                   │
│          [继续努力] [查看详情]       │
│                                   │
└─────────────────────────────────────┘
```

**设计要求**：
- 使用明亮的色彩和动画效果
- 积分变化有数字滚动动画
- 新徽章获得时有光芒特效
- 排行榜支持下拉刷新
- 等级进度条实时更新

## 5. 技术实现要点

### 5.1 积分计算系统
```javascript
class PointsManager {
  // 计算练习积分
  calculatePracticePoints(result) {
    let points = 0;
    const { correctCount, totalCount, timeSpent, grade, streak } = result;
    
    // 基础积分
    points += correctCount * 10;
    
    // 全对奖励
    if (correctCount === totalCount) {
      points += 20;
    }
    
    // 连击奖励
    if (streak >= 5) {
      points += Math.floor(streak / 5) * 10;
    }
    
    // 速度奖励
    const avgTime = timeSpent / totalCount;
    if (avgTime < 3) {
      points += 5;
    }
    
    // 年级系数
    const gradeMultiplier = 1 + (grade - 1) * 0.1;
    points = Math.floor(points * gradeMultiplier);
    
    return points;
  }

  // 检查等级提升
  async checkLevelUp(userId, newPoints) {
    const user = await this.getUserInfo(userId);
    const oldLevel = this.calculateLevel(user.totalPoints);
    const newLevel = this.calculateLevel(user.totalPoints + newPoints);
    
    if (newLevel > oldLevel) {
      await this.processLevelUp(userId, newLevel);
      return { levelUp: true, newLevel, oldLevel };
    }
    
    return { levelUp: false };
  }

  // 计算等级
  calculateLevel(points) {
    const levels = [0, 100, 300, 600, 1000, 1500, 2200, 3000, 4000, 5500];
    for (let i = levels.length - 1; i >= 0; i--) {
      if (points >= levels[i]) {
        return i + 1;
      }
    }
    return 1;
  }
}
```

### 5.2 成就系统实现
```javascript
class AchievementManager {
  // 检查成就完成
  async checkAchievements(userId, action) {
    const userStats = await this.getUserStats(userId);
    const availableAchievements = await this.getAvailableAchievements(userId);
    
    const newAchievements = [];
    
    for (const achievement of availableAchievements) {
      if (this.checkAchievementCondition(achievement, userStats, action)) {
        await this.grantAchievement(userId, achievement.id);
        newAchievements.push(achievement);
      }
    }
    
    return newAchievements;
  }

  // 检查具体成就条件
  checkAchievementCondition(achievement, stats, action) {
    switch (achievement.type) {
      case 'consecutive_days':
        return stats.consecutiveDays >= achievement.condition.days;
      case 'accuracy_rate':
        return stats.averageAccuracy >= achievement.condition.rate;
      case 'practice_count':
        return stats.totalPractices >= achievement.condition.count;
      case 'perfect_streak':
        return action.type === 'practice_complete' && 
               action.perfectStreak >= achievement.condition.streak;
      default:
        return false;
    }
  }

  // 发放成就奖励
  async grantAchievement(userId, achievementId) {
    const achievement = await this.getAchievementById(achievementId);
    
    // 记录获得成就
    await this.recordUserAchievement(userId, achievementId);
    
    // 发放积分奖励
    if (achievement.pointsReward > 0) {
      await this.pointsManager.addPoints(userId, achievement.pointsReward);
    }
    
    // 推送通知
    await this.notificationManager.sendAchievementNotification(userId, achievement);
  }
}
```

### 5.3 排行榜系统实现
```javascript
class RankingManager {
  // 更新排行榜
  async updateRanking(userId, points) {
    const rankings = ['friends', 'class', 'grade', 'school'];
    
    for (const type of rankings) {
      await this.updateSpecificRanking(type, userId, points);
    }
  }

  // 更新特定类型排行榜
  async updateSpecificRanking(type, userId, points) {
    const key = `ranking:${type}:week:${this.getCurrentWeek()}`;
    
    // 更新Redis排行榜
    await redis.zadd(key, points, userId);
    
    // 设置过期时间
    await redis.expire(key, 7 * 24 * 3600);
    
    // 检查排名变化
    const rank = await redis.zrevrank(key, userId);
    const oldRank = await this.getOldRank(type, userId);
    
    if (rank !== oldRank) {
      await this.notifyRankChange(userId, type, oldRank, rank);
    }
  }

  // 获取排行榜数据
  async getRankingList(type, userId, page = 1, limit = 20) {
    const key = `ranking:${type}:week:${this.getCurrentWeek()}`;
    const offset = (page - 1) * limit;
    
    // 获取排行榜数据
    const rankings = await redis.zrevrange(key, offset, offset + limit - 1, 'WITHSCORES');
    
    // 获取用户信息
    const userIds = rankings.filter((_, index) => index % 2 === 0);
    const scores = rankings.filter((_, index) => index % 2 === 1);
    const users = await this.getUserInfoBatch(userIds);
    
    // 组织返回数据
    const result = users.map((user, index) => ({
      rank: offset + index + 1,
      userId: user.id,
      nickname: user.nickname,
      avatar: user.avatar,
      points: parseInt(scores[index])
    }));
    
    // 获取当前用户排名
    const userRank = await redis.zrevrank(key, userId);
    const userScore = await redis.zscore(key, userId);
    
    return {
      list: result,
      userRank: userRank !== null ? userRank + 1 : null,
      userScore: userScore ? parseInt(userScore) : 0
    };
  }
}
```

### 5.4 实时通知系统
```javascript
class NotificationManager {
  // 发送成就通知
  async sendAchievementNotification(userId, achievement) {
    const notification = {
      type: 'achievement',
      title: '🎉 获得新徽章！',
      content: `恭喜您获得"${achievement.name}"徽章！`,
      data: {
        achievementId: achievement.id,
        pointsReward: achievement.pointsReward
      }
    };
    
    await this.sendNotification(userId, notification);
  }

  // 发送等级提升通知
  async sendLevelUpNotification(userId, newLevel) {
    const notification = {
      type: 'level_up',
      title: '🎊 等级提升！',
      content: `恭喜您升到了${newLevel}级！`,
      data: {
        newLevel: newLevel
      }
    };
    
    await this.sendNotification(userId, notification);
  }

  // 发送排名变化通知
  async sendRankChangeNotification(userId, rankType, oldRank, newRank) {
    if (newRank < oldRank) {
      const notification = {
        type: 'rank_up',
        title: '📈 排名上升！',
        content: `您在${this.getRankTypeName(rankType)}中排名上升到第${newRank}名！`,
        data: {
          rankType,
          oldRank,
          newRank
        }
      };
      
      await this.sendNotification(userId, notification);
    }
  }
}
```

## 6. 数据接口定义

### 6.1 获取成就中心数据
```
GET /api/achievements/overview

Response:
{
  "code": 0,
  "data": {
    "level": {
      "current": 5,
      "name": "小专家",
      "currentPoints": 1260,
      "nextLevelPoints": 1500,
      "progress": 0.84
    },
    "weeklyPoints": {
      "total": 186,
      "daily": [20, 35, 28, 0, 0, 0, 0]
    },
    "recentAchievements": [
      {
        "id": 1,
        "name": "勤奋学者",
        "icon": "🏆",
        "obtainedAt": "2024-03-15"
      }
    ]
  }
}
```

### 6.2 获取排行榜数据
```
GET /api/rankings/{type}?page=1&limit=20

Response:
{
  "code": 0,
  "data": {
    "list": [
      {
        "rank": 1,
        "userId": 123,
        "nickname": "小红同学",
        "avatar": "头像URL",
        "points": 298
      }
    ],
    "userRank": 5,
    "userScore": 186,
    "total": 50
  }
}
```

### 6.3 积分记录接口
```
POST /api/points/add

Request:
{
  "userId": 123,
  "action": "practice_complete",
  "points": 35,
  "details": {
    "practiceId": 456,
    "correctCount": 10,
    "totalCount": 10,
    "bonusType": "perfect_score"
  }
}

Response:
{
  "code": 0,
  "data": {
    "pointsAdded": 35,
    "totalPoints": 1295,
    "levelUp": false,
    "newAchievements": []
  }
}
```

## 7. 测试用例

### 7.1 积分系统测试
- **基础积分**：完成练习获得对应积分
- **奖励积分**：全对、连击等额外奖励
- **等级提升**：积分达到阈值时等级提升
- **数据一致性**：多设备登录积分数据一致

### 7.2 成就系统测试
- **成就触发**：满足条件时自动获得成就
- **重复检查**：不会重复获得相同成就
- **奖励发放**：成就奖励正确发放
- **进度更新**：成就进度实时更新

### 7.3 排行榜测试
- **实时更新**：积分变化后排行榜及时更新
- **分类正确**：好友、班级等分类准确
- **权限控制**：仅显示有权限查看的用户
- **性能测试**：大量用户时排行榜性能

该成就系统设计通过丰富的游戏化元素，将枯燥的学习过程转化为有趣的挑战游戏，有效激发学生的学习兴趣和持续动力，同时通过社交元素增强学习的互动性和竞争性。 
#!/usr/bin/env node

/**
 * 教材配置索引工具命令行界面
 */

const indexer = require('./indexer');

// 帮助信息
const helpText = `
教材配置索引工具 - 命令行界面

用法:
  node cli.js <命令> [参数]

可用命令:
  stats                           - 显示教材统计信息
  versions                        - 列出所有版本
  grades <版本ID>                 - 列出指定版本的所有年级
  terms <版本ID> <年级ID>         - 列出指定版本和年级的所有学期
  lessons <版本ID> <年级ID> <学期ID> - 列出指定版本、年级和学期的所有课程
  table <版本ID> <年级ID> <学期ID> <课程ID> <表类型> - 显示指定词表内容
  search <关键词>                 - 搜索字词
  export <版本ID> <年级ID> <学期ID> <课程ID> <表类型> - 导出词表到文件

表类型: shiZiBiao(识字表), xieZiBiao(写字表), ciYuBiao(词语表)

示例:
  node cli.js stats
  node cli.js versions
  node cli.js grades renjiaoban
  node cli.js terms renjiaoban grade1
  node cli.js lessons renjiaoban grade1 term1
  node cli.js table renjiaoban grade1 term1 lesson1 shiZiBiao
  node cli.js search 天
  node cli.js export renjiaoban grade1 term1 lesson1 shiZiBiao
`;

// 处理命令行参数
function processArgs() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === 'help' || args[0] === '--help' || args[0] === '-h') {
    console.log(helpText);
    return;
  }
  
  const command = args[0];
  
  switch (command) {
    case 'stats':
      const stats = indexer.getStatistics();
      console.log('教材统计信息:');
      console.log(JSON.stringify(stats, null, 2));
      break;
      
    case 'versions':
      const versions = indexer.getAllVersions();
      console.log('所有版本:');
      versions.forEach(v => {
        console.log(`- ${v.id}: ${v.name}`);
      });
      break;
      
    case 'grades':
      if (args.length < 2) {
        console.error('错误: 缺少版本ID参数');
        console.log('用法: node cli.js grades <版本ID>');
        return;
      }
      
      const versionId = args[1];
      const grades = indexer.getGrades(versionId);
      
      console.log(`${versionId} 的所有年级:`);
      grades.forEach(g => {
        console.log(`- ${g.id}: ${g.name}`);
      });
      break;
      
    case 'terms':
      if (args.length < 3) {
        console.error('错误: 缺少参数');
        console.log('用法: node cli.js terms <版本ID> <年级ID>');
        return;
      }
      
      const vId = args[1];
      const gradeId = args[2];
      const terms = indexer.getTerms(vId, gradeId);
      
      console.log(`${vId}.${gradeId} 的所有学期:`);
      terms.forEach(t => {
        console.log(`- ${t.id}: ${t.name}`);
      });
      break;
      
    case 'lessons':
      if (args.length < 4) {
        console.error('错误: 缺少参数');
        console.log('用法: node cli.js lessons <版本ID> <年级ID> <学期ID>');
        return;
      }
      
      const vid = args[1];
      const gid = args[2];
      const termId = args[3];
      const lessons = indexer.getLessons(vid, gid, termId);
      
      console.log(`${vid}.${gid}.${termId} 的所有课程:`);
      lessons.forEach(l => {
        console.log(`- ${l.id}: ${l.name} (${l.title})`);
      });
      break;
      
    case 'table':
      if (args.length < 6) {
        console.error('错误: 缺少参数');
        console.log('用法: node cli.js table <版本ID> <年级ID> <学期ID> <课程ID> <表类型>');
        return;
      }
      
      const v = args[1];
      const g = args[2];
      const t = args[3];
      const lessonId = args[4];
      const tableType = args[5];
      
      const table = indexer.findTable(v, g, t, lessonId, tableType);
      
      if (!table) {
        console.error(`未找到表: ${v}.${g}.${t}.${lessonId}.${tableType}`);
        return;
      }
      
      console.log(`${v}.${g}.${t}.${lessonId}.${tableType} 的内容:`);
      table.forEach(item => {
        console.log(`- ${item.word}: ${item.pinyin}`);
      });
      break;
      
    case 'search':
      if (args.length < 2) {
        console.error('错误: 缺少搜索关键词');
        console.log('用法: node cli.js search <关键词>');
        return;
      }
      
      const query = args[1];
      const results = indexer.searchWords(query);
      
      console.log(`搜索结果 "${query}":`);
      if (results.length === 0) {
        console.log('未找到匹配结果');
      } else {
        results.forEach((result, index) => {
          console.log(`${index + 1}. ${result.word} (${result.pinyin})`);
          console.log(`   ${result.type} - ${result.version} ${result.grade}${result.term} ${result.lesson} "${result.title}"`);
          console.log(`   路径: ${result.path}`);
          console.log();
        });
        console.log(`共找到 ${results.length} 条匹配结果`);
      }
      break;
      
    case 'export':
      if (args.length < 6) {
        console.error('错误: 缺少参数');
        console.log('用法: node cli.js export <版本ID> <年级ID> <学期ID> <课程ID> <表类型>');
        return;
      }
      
      const expV = args[1];
      const expG = args[2];
      const expT = args[3];
      const expL = args[4];
      const expTable = args[5];
      
      const success = indexer.exportTable(expV, expG, expT, expL, expTable);
      
      if (success) {
        console.log(`成功导出 ${expV}.${expG}.${expT}.${expL}.${expTable} 的内容`);
      } else {
        console.error(`导出失败: ${expV}.${expG}.${expT}.${expL}.${expTable}`);
      }
      break;
      
    default:
      console.error(`未知命令: ${command}`);
      console.log(helpText);
      break;
  }
}

// 执行
processArgs(); 
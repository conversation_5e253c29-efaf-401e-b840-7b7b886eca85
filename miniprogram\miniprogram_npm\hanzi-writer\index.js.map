{"version": 3, "sources": ["hanzi-writer.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*!\n * <PERSON>zi Writer v2.3.0\n * https://chanind.github.io/hanzi-writer\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"HanziWriter\"] = factory();\n\telse\n\t\troot[\"HanziWriter\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 9);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction emptyFunc() {}\n\nvar performanceNow = global.performance && function () {\n  return global.performance.now();\n} || function () {\n  return Date.now();\n};\nvar requestAnimationFrame = global.requestAnimationFrame || function (callback) {\n  return setTimeout(function () {\n    return callback(performanceNow());\n  }, 1000 / 60);\n};\nvar cancelAnimationFrame = global.cancelAnimationFrame || clearTimeout;\n\n// Object.assign polyfill, because IE :/\nvar _assign = function _assign(target) {\n  var overrideTarget = Object(target);\n\n  for (var _len = arguments.length, overrides = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    overrides[_key - 1] = arguments[_key];\n  }\n\n  overrides.forEach(function (override) {\n    if (override != null) {\n      for (var key in override) {\n        if (Object.prototype.hasOwnProperty.call(override, key)) {\n          overrideTarget[key] = override[key];\n        }\n      }\n    }\n  });\n  return overrideTarget;\n};\n\nvar assign = Object.assign || _assign;\n\nvar arrLast = function arrLast(arr) {\n  return arr[arr.length - 1];\n};\n\nfunction copyAndMergeDeep(base, override) {\n  var output = assign({}, base);\n  for (var key in override) {\n    // eslint-disable-line guard-for-in\n    // skipping hasOwnProperty check for performance reasons - we shouldn't be passing any objects\n    // in here that aren't plain objects anyway and this is a hot code path\n    var baseVal = base[key];\n    var overrideVal = override[key];\n    if (baseVal === overrideVal) continue; // eslint-disable-line no-continue\n    if (baseVal && overrideVal && (typeof baseVal === 'undefined' ? 'undefined' : _typeof(baseVal)) === 'object' && (typeof overrideVal === 'undefined' ? 'undefined' : _typeof(overrideVal)) === 'object' && !Array.isArray(overrideVal)) {\n      output[key] = copyAndMergeDeep(baseVal, overrideVal);\n    } else {\n      output[key] = overrideVal;\n    }\n  }\n  return output;\n}\n\nfunction inflate(scope, obj) {\n  var parts = scope.split('.');\n  var final = {};\n  var current = final;\n  for (var i = 0; i < parts.length; i++) {\n    var cap = i === parts.length - 1 ? obj : {};\n    current[parts[i]] = cap;\n    current = cap;\n  }\n  return final;\n}\n\nfunction callIfExists(callback, arg) {\n  if (callback) callback(arg);\n  return arg;\n}\n\nvar count = 0;\nfunction counter() {\n  count++;\n  return count;\n}\n\nfunction average(arr) {\n  var sum = arr.reduce(function (acc, val) {\n    return val + acc;\n  }, 0);\n  return sum / arr.length;\n}\n\nfunction timeout() {\n  var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n  return new Promise(function (resolve, reject) {\n    setTimeout(resolve, duration);\n  });\n}\n\nfunction colorStringToVals(colorString) {\n  var normalizedColor = colorString.toUpperCase().trim();\n  // based on https://stackoverflow.com/a/21648508\n  if (/^#([A-F0-9]{3}){1,2}$/.test(normalizedColor)) {\n    var hexParts = normalizedColor.substring(1).split('');\n    if (hexParts.length === 3) {\n      hexParts = [hexParts[0], hexParts[0], hexParts[1], hexParts[1], hexParts[2], hexParts[2]];\n    }\n    var hexStr = '' + hexParts.join('');\n    return {\n      r: parseInt(hexStr.slice(0, 2), 16),\n      g: parseInt(hexStr.slice(2, 4), 16),\n      b: parseInt(hexStr.slice(4, 6), 16),\n      a: 1\n    };\n  }\n  var rgbMatch = normalizedColor.match(/^RGBA?\\((\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)(?:\\s*,\\s*(\\d*\\.?\\d+))?\\)$/);\n  if (rgbMatch) {\n    return {\n      r: parseInt(rgbMatch[1], 10),\n      g: parseInt(rgbMatch[2], 10),\n      b: parseInt(rgbMatch[3], 10),\n      a: parseFloat(rgbMatch[4] || 1, 10)\n    };\n  }\n  throw new Error('Invalid color: ' + colorString);\n}\n\nvar trim = function trim(string) {\n  return string.replace(/^\\s+/, '').replace(/\\s+$/, '');\n};\n\n// return a new array-like object with int keys where each key is item\n// ex: objRepeat({x: 8}, 3) === {0: {x: 8}, 1: {x: 8}, 2: {x: 8}}\nvar objRepeat = function objRepeat(item, times) {\n  var obj = {};\n  for (var i = 0; i < times; i++) {\n    obj[i] = item;\n  }\n  return obj;\n};\n\nvar ua = global.navigator && global.navigator.userAgent || '';\nvar isMsBrowser = ua.indexOf('MSIE ') > 0 || ua.indexOf('Trident/') > 0 || ua.indexOf('Edge/') > 0;\n\nmodule.exports = {\n  _assign: _assign,\n  arrLast: arrLast,\n  assign: assign,\n  average: average,\n  callIfExists: callIfExists,\n  cancelAnimationFrame: cancelAnimationFrame,\n  colorStringToVals: colorStringToVals,\n  copyAndMergeDeep: copyAndMergeDeep,\n  counter: counter,\n  emptyFunc: emptyFunc,\n  inflate: inflate,\n  objRepeat: objRepeat,\n  performanceNow: performanceNow,\n  requestAnimationFrame: requestAnimationFrame,\n  timeout: timeout,\n  trim: trim,\n  isMsBrowser: isMsBrowser\n};\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports) {\n\nvar g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    average = _require.average,\n    arrLast = _require.arrLast;\n\nvar subtract = function subtract(p1, p2) {\n  return { x: p1.x - p2.x, y: p1.y - p2.y };\n};\nvar magnitude = function magnitude(point) {\n  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));\n};\nvar distance = function distance(point1, point2) {\n  return magnitude(subtract(point1, point2));\n};\nvar equals = function equals(point1, point2) {\n  return point1.x === point2.x && point1.y === point2.y;\n};\nvar round = function round(point) {\n  var precision = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n\n  var multiplier = precision * 10;\n  return {\n    x: Math.round(multiplier * point.x) / multiplier,\n    y: Math.round(multiplier * point.y) / multiplier\n  };\n};\n\nvar length = function length(points) {\n  var lastPoint = points[0];\n  var pointsSansFirst = points.slice(1);\n  return pointsSansFirst.reduce(function (acc, point) {\n    var dist = distance(point, lastPoint);\n    lastPoint = point;\n    return acc + dist;\n  }, 0);\n};\n\nvar cosineSimilarity = function cosineSimilarity(point1, point2) {\n  var rawDotProduct = point1.x * point2.x + point1.y * point2.y;\n  return rawDotProduct / magnitude(point1) / magnitude(point2);\n};\n\n// return a new point, p3, which is on the same line as p1 and p2, but distance away\n// from p2. p1, p2, p3 will always lie on the line in that order\nvar _extendPointOnLine = function _extendPointOnLine(p1, p2, dist) {\n  var vect = subtract(p2, p1);\n  var norm = dist / magnitude(vect);\n  return { x: p2.x + norm * vect.x, y: p2.y + norm * vect.y };\n};\n\n// based on http://www.kr.tuwien.ac.at/staff/eiter/et-archive/cdtr9464.pdf\nvar frechetDist = function frechetDist(curve1, curve2) {\n  var results = [];\n  for (var i = 0; i < curve1.length; i++) {\n    results.push([]);\n    for (var j = 0; j < curve2.length; j++) {\n      results[i].push(-1);\n    }\n  }\n\n  var recursiveCalc = function recursiveCalc(i, j) {\n    if (results[i][j] > -1) return results[i][j];\n    if (i === 0 && j === 0) {\n      results[i][j] = distance(curve1[0], curve2[0]);\n    } else if (i > 0 && j === 0) {\n      results[i][j] = Math.max(recursiveCalc(i - 1, 0), distance(curve1[i], curve2[0]));\n    } else if (i === 0 && j > 0) {\n      results[i][j] = Math.max(recursiveCalc(0, j - 1), distance(curve1[0], curve2[j]));\n    } else if (i > 0 && j > 0) {\n      results[i][j] = Math.max(Math.min(recursiveCalc(i - 1, j), recursiveCalc(i - 1, j - 1), recursiveCalc(i, j - 1)), distance(curve1[i], curve2[j]));\n    } else {\n      results[i][j] = Infinity;\n    }\n    return results[i][j];\n  };\n\n  return recursiveCalc(curve1.length - 1, curve2.length - 1);\n};\n\n// break up long segments in the curve into smaller segments of len maxLen or smaller\nvar subdivideCurve = function subdivideCurve(curve) {\n  var maxLen = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.05;\n\n  var newCurve = curve.slice(0, 1);\n  curve.slice(1).forEach(function (point) {\n    var prevPoint = newCurve[newCurve.length - 1];\n    var segLen = distance(point, prevPoint);\n    if (segLen > maxLen) {\n      var numNewPoints = Math.ceil(segLen / maxLen);\n      var newSegLen = segLen / numNewPoints;\n      for (var i = 0; i < numNewPoints; i++) {\n        newCurve.push(_extendPointOnLine(point, prevPoint, -1 * newSegLen * (i + 1)));\n      }\n    } else {\n      newCurve.push(point);\n    }\n  });\n  return newCurve;\n};\n\n// redraw the curve using numPoints equally spaced out along the length of the curve\nvar outlineCurve = function outlineCurve(curve) {\n  var numPoints = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 30;\n\n  var curveLen = length(curve);\n  var segmentLen = curveLen / (numPoints - 1);\n  var outlinePoints = [curve[0]];\n  var endPoint = arrLast(curve);\n  var remainingCurvePoints = curve.slice(1);\n  for (var i = 0; i < numPoints - 2; i++) {\n    var lastPoint = arrLast(outlinePoints);\n    var remainingDist = segmentLen;\n    var outlinePointFound = false;\n    while (!outlinePointFound) {\n      var nextPointDist = distance(lastPoint, remainingCurvePoints[0]);\n      if (nextPointDist < remainingDist) {\n        remainingDist -= nextPointDist;\n        lastPoint = remainingCurvePoints.shift();\n      } else {\n        var nextPoint = _extendPointOnLine(lastPoint, remainingCurvePoints[0], remainingDist - nextPointDist);\n        outlinePoints.push(nextPoint);\n        outlinePointFound = true;\n      }\n    }\n  }\n  outlinePoints.push(endPoint);\n  return outlinePoints;\n};\n\n// translate and scale from https://en.wikipedia.org/wiki/Procrustes_analysis\nvar normalizeCurve = function normalizeCurve(curve) {\n  var outlinedCurve = outlineCurve(curve);\n  var meanX = average(outlinedCurve.map(function (point) {\n    return point.x;\n  }));\n  var meanY = average(outlinedCurve.map(function (point) {\n    return point.y;\n  }));\n  var mean = { x: meanX, y: meanY };\n  var translatedCurve = outlinedCurve.map(function (point) {\n    return subtract(point, mean);\n  });\n  var scale = Math.sqrt(average([Math.pow(translatedCurve[0].x, 2) + Math.pow(translatedCurve[0].y, 2), Math.pow(arrLast(translatedCurve).x, 2) + Math.pow(arrLast(translatedCurve).y, 2)]));\n  var scaledCurve = translatedCurve.map(function (point) {\n    return { x: point.x / scale, y: point.y / scale };\n  });\n  return subdivideCurve(scaledCurve);\n};\n\n// rotate around the origin\nvar rotate = function rotate(curve, theta) {\n  return curve.map(function (point) {\n    return {\n      x: Math.cos(theta) * point.x - Math.sin(theta) * point.y,\n      y: Math.sin(theta) * point.x + Math.cos(theta) * point.y\n    };\n  });\n};\n\n// remove intermediate points that are on the same line as the points to either side\nvar _filterParallelPoints = function _filterParallelPoints(points) {\n  if (points.length < 3) return points;\n  var filteredPoints = [points[0], points[1]];\n  points.slice(2).forEach(function (point, i) {\n    var numFilteredPoints = filteredPoints.length;\n    var curVect = subtract(point, filteredPoints[numFilteredPoints - 1]);\n    var prevVect = subtract(filteredPoints[numFilteredPoints - 1], filteredPoints[numFilteredPoints - 2]);\n    // this is the z coord of the cross-product. If this is 0 then they're parallel\n    var isParallel = curVect.y * prevVect.x - curVect.x * prevVect.y === 0;\n    if (isParallel) {\n      filteredPoints.pop();\n    }\n    filteredPoints.push(point);\n  });\n  return filteredPoints;\n};\n\nfunction getPathString(points) {\n  var close = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var start = round(points[0]);\n  var remainingPoints = points.slice(1);\n  var pathString = 'M ' + start.x + ' ' + start.y;\n  remainingPoints.forEach(function (point) {\n    var roundedPoint = round(point);\n    pathString += ' L ' + roundedPoint.x + ' ' + roundedPoint.y;\n  });\n  if (close) pathString += 'Z';\n  return pathString;\n}\n\n// take points on a path and move their start point backwards by distance\nvar extendStart = function extendStart(points, dist) {\n  var filteredPoints = _filterParallelPoints(points);\n  if (filteredPoints.length < 2) return filteredPoints;\n  var p1 = filteredPoints[1];\n  var p2 = filteredPoints[0];\n  var newStart = _extendPointOnLine(p1, p2, dist);\n  var extendedPoints = filteredPoints.slice(1);\n  extendedPoints.unshift(newStart);\n  return extendedPoints;\n};\n\nmodule.exports = {\n  round: round,\n  equals: equals,\n  distance: distance,\n  getPathString: getPathString,\n  frechetDist: frechetDist,\n  length: length,\n  rotate: rotate,\n  subtract: subtract,\n  extendStart: extendStart,\n  cosineSimilarity: cosineSimilarity,\n  outlineCurve: outlineCurve,\n  _extendPointOnLine: _extendPointOnLine,\n  _filterParallelPoints: _filterParallelPoints,\n  subdivideCurve: subdivideCurve,\n  normalizeCurve: normalizeCurve\n};\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nfunction createElm(elmType) {\n  return global.document.createElementNS('http://www.w3.org/2000/svg', elmType);\n}\n\nfunction attr(elm, name, value) {\n  elm.setAttributeNS(null, name, value);\n}\n\nfunction attrs(elm, attrsMap) {\n  Object.keys(attrsMap).forEach(function (attrName) {\n    return attr(elm, attrName, attrsMap[attrName]);\n  });\n}\n\n// inspired by https://talk.observablehq.com/t/hanzi-writer-renders-incorrectly-inside-an-observable-notebook-on-a-mobile-browser/1898\nfunction urlIdRef(id) {\n  var prefix = '';\n  if (global.location && global.location.href) {\n    prefix = global.location.href.replace(/#[^#]*$/, '');\n  }\n  return 'url(' + prefix + '#' + id + ')';\n}\n\nfunction removeElm(elm) {\n  if (elm) elm.parentNode.removeChild(elm);\n}\n\nmodule.exports = { createElm: createElm, attrs: attrs, attr: attr, removeElm: removeElm, urlIdRef: urlIdRef };\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar Mutation = __webpack_require__(5);\n\nvar _require = __webpack_require__(0),\n    objRepeat = _require.objRepeat;\n\nvar showStrokes = function showStrokes(charName, character, duration) {\n  return [new Mutation('character.' + charName + '.strokes', objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length), { duration: duration, force: true })];\n};\n\nvar showCharacter = function showCharacter(charName, character, duration) {\n  return [new Mutation('character.' + charName, {\n    opacity: 1,\n    strokes: objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length)\n  }, { duration: duration, force: true })];\n};\n\nvar hideCharacter = function hideCharacter(charName, character, duration) {\n  return [new Mutation('character.' + charName + '.opacity', 0, { duration: duration, force: true })].concat(showStrokes(charName, character, 0));\n};\n\nvar updateColor = function updateColor(colorName, colorVal, duration) {\n  return [new Mutation('options.' + colorName, colorVal, { duration: duration })];\n};\n\nvar highlightStroke = function highlightStroke(stroke, color, speed) {\n  var strokeNum = stroke.strokeNum;\n  var duration = (stroke.getLength() + 600) / (3 * speed);\n  return [new Mutation('character.highlight.strokeColor', color), new Mutation('character.highlight', {\n    opacity: 1,\n    strokes: _defineProperty({}, strokeNum, {\n      displayPortion: 0,\n      opacity: 0\n    })\n  }), new Mutation('character.highlight.strokes.' + strokeNum, {\n    displayPortion: 1,\n    opacity: 1\n  }, { duration: duration }), new Mutation('character.highlight.strokes.' + strokeNum + '.opacity', 0, { duration: duration })];\n};\n\nvar animateStroke = function animateStroke(charName, stroke, speed) {\n  var strokeNum = stroke.strokeNum;\n  var duration = (stroke.getLength() + 600) / (3 * speed);\n  return [new Mutation('character.' + charName, {\n    opacity: 1,\n    strokes: _defineProperty({}, strokeNum, {\n      displayPortion: 0,\n      opacity: 1\n    })\n  }), new Mutation('character.' + charName + '.strokes.' + strokeNum + '.displayPortion', 1, { duration: duration })];\n};\n\nvar animateSingleStroke = function animateSingleStroke(charName, character, strokeNum, speed) {\n  var mutationStateFunc = function mutationStateFunc(state) {\n    var curCharState = state.character[charName];\n    var mutationState = {\n      opacity: 1,\n      strokes: {}\n    };\n    for (var i = 0; i < character.strokes.length; i++) {\n      mutationState.strokes[i] = {\n        opacity: curCharState.opacity * curCharState.strokes[i].opacity\n      };\n    }\n    return mutationState;\n  };\n  return [new Mutation('character.' + charName, mutationStateFunc)].concat(animateStroke(charName, character.strokes[strokeNum], speed));\n};\n\nvar showStroke = function showStroke(charName, strokeNum, duration) {\n  return [new Mutation('character.' + charName + '.strokes.' + strokeNum, {\n    displayPortion: 1,\n    opacity: 1\n  }, { duration: duration, force: true })];\n};\n\nvar animateCharacter = function animateCharacter(charName, character, fadeDuration, speed, delayBetweenStrokes) {\n  var mutations = hideCharacter(charName, character, fadeDuration);\n  mutations = mutations.concat(showStrokes(charName, character, 0));\n  mutations.push(new Mutation('character.' + charName, {\n    opacity: 1,\n    strokes: objRepeat({ opacity: 0 }, character.strokes.length)\n  }, { force: true }));\n  character.strokes.forEach(function (stroke, i) {\n    if (i > 0) mutations.push(new Mutation.Delay(delayBetweenStrokes));\n    mutations = mutations.concat(animateStroke(charName, stroke, speed));\n  });\n  return mutations;\n};\n\nvar animateCharacterLoop = function animateCharacterLoop(charName, character, fadeDuration, speed, delayBetweenStrokes, delayBetweenLoops) {\n  var mutations = animateCharacter(charName, character, fadeDuration, speed, delayBetweenStrokes);\n  mutations.push(new Mutation.Delay(delayBetweenLoops));\n  return mutations;\n};\n\nmodule.exports = {\n  showStrokes: showStrokes,\n  showCharacter: showCharacter,\n  hideCharacter: hideCharacter,\n  highlightStroke: highlightStroke,\n  animateCharacter: animateCharacter,\n  animateCharacterLoop: animateCharacterLoop,\n  animateStroke: animateStroke,\n  animateSingleStroke: animateSingleStroke,\n  showStroke: showStroke,\n  updateColor: updateColor\n};\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    inflate = _require.inflate,\n    performanceNow = _require.performanceNow,\n    requestAnimationFrame = _require.requestAnimationFrame,\n    cancelAnimationFrame = _require.cancelAnimationFrame;\n\n// ------ Mutation class --------\n\nvar getPartialValues = function getPartialValues(startValues, endValues, progress) {\n  var target = {};\n  for (var key in endValues) {\n    // eslint-disable-line guard-for-in\n    // skipping hasOwnProperty check for performance reasons - we shouldn't be passing any objects\n    // in here that aren't plain objects anyway and this is a hot code path\n    var endValue = endValues[key];\n    var startValue = startValues[key];\n    if (endValue >= 0) {\n      target[key] = progress * (endValue - startValue) + startValue;\n    } else {\n      target[key] = getPartialValues(startValue, endValue, progress);\n    }\n  }\n  return target;\n};\n\nvar isAlreadyAtEnd = function isAlreadyAtEnd(startValues, endValues) {\n  for (var key in endValues) {\n    if (endValues.hasOwnProperty(key)) {\n      var endValue = endValues[key];\n      var startValue = startValues[key];\n      if (endValue >= 0) {\n        if (endValue !== startValue) return false;\n      } else if (!isAlreadyAtEnd(startValue, endValue)) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\n\n// from https://github.com/maxwellito/vivus\nvar ease = function ease(x) {\n  return -Math.cos(x * Math.PI) / 2 + 0.5;\n};\n\nfunction Mutation(scope, valuesOrCallable) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  this.scope = scope;\n  this._valuesOrCallable = valuesOrCallable;\n  this._duration = options.duration || 0;\n  this._force = options.force;\n  this._pausedDuration = 0;\n  this._tickBound = this._tick.bind(this);\n  this._startPauseTime = null;\n}\n\nMutation.prototype.run = function (renderState) {\n  var _this = this;\n\n  if (!this._values) this._inflateValues(renderState);\n  if (this._duration === 0) renderState.updateState(this._values);\n  if (this._duration === 0 || isAlreadyAtEnd(renderState.state, this._values)) {\n    return Promise.resolve();\n  }\n  this._renderState = renderState;\n  this._startState = renderState.state;\n  this._startTime = performanceNow();\n  this._frameHandle = requestAnimationFrame(this._tickBound);\n  return new Promise(function (resolve) {\n    _this._resolve = resolve;\n  });\n};\n\nMutation.prototype.pause = function () {\n  if (this._startPauseTime !== null) return;\n  if (this._frameHandle) cancelAnimationFrame(this._frameHandle);\n  this._startPauseTime = performanceNow();\n};\n\nMutation.prototype.resume = function () {\n  if (this._startPauseTime === null) return;\n  this._frameHandle = requestAnimationFrame(this._tickBound);\n  this._pausedDuration += performanceNow() - this._startPauseTime;\n  this._startPauseTime = null;\n};\n\nMutation.prototype._tick = function (timing) {\n  if (this._startPauseTime !== null) return;\n  var progress = Math.min(1, (timing - this._startTime - this._pausedDuration) / this._duration);\n  if (progress === 1) {\n    this._renderState.updateState(this._values);\n    this._frameHandle = null;\n    this.cancel(this._renderState);\n  } else {\n    var easedProgress = ease(progress);\n    this._renderState.updateState(getPartialValues(this._startState, this._values, easedProgress));\n    this._frameHandle = requestAnimationFrame(this._tickBound);\n  }\n};\n\nMutation.prototype._inflateValues = function (renderState) {\n  var values = this._valuesOrCallable;\n  if (typeof this._valuesOrCallable === 'function') {\n    values = this._valuesOrCallable(renderState.state);\n  }\n  this._values = inflate(this.scope, values);\n};\n\nMutation.prototype.cancel = function (renderState) {\n  if (this._resolve) this._resolve();\n  this._resolve = null;\n  if (this._frameHandle) cancelAnimationFrame(this._frameHandle);\n  this._frameHandle = null;\n  if (this._force) {\n    if (!this._values) this._inflateValues(renderState);\n    renderState.updateState(this._values);\n  }\n};\n\n// ------ Mutation.Delay Class --------\n\nfunction Delay(duration) {\n  this._duration = duration;\n  this._startTime = null;\n  this._paused = false;\n}\n\nDelay.prototype.pause = function () {\n  if (this._paused) return;\n  // to pause, clear the timeout and rewrite this._duration with whatever time is remaining\n  var elapsedDelay = performanceNow() - this._startTime;\n  this._duration = Math.max(0, this._duration - elapsedDelay);\n  clearTimeout(this._timeout);\n  this._paused = true;\n};\n\nDelay.prototype.resume = function () {\n  var _this2 = this;\n\n  if (!this._paused) return;\n  this._startTime = performanceNow();\n  this._timeout = setTimeout(function () {\n    return _this2.cancel();\n  }, this._duration);\n  this._paused = false;\n};\n\nDelay.prototype.run = function () {\n  var _this3 = this;\n\n  var timeoutPromise = new Promise(function (resolve) {\n    _this3._resolve = resolve;\n  });\n  this._startTime = performanceNow();\n  this._timeout = setTimeout(function () {\n    return _this3.cancel();\n  }, this._duration);\n  return timeoutPromise;\n};\n\nDelay.prototype.cancel = function () {\n  clearTimeout(this._timeout);\n  if (this._resolve) this._resolve();\n  this._resolve = false;\n};\n\nMutation.Delay = Delay;\n\n// -------------------------------------\n\n\nmodule.exports = Mutation;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nfunction StrokeRendererBase() {}\n\nStrokeRendererBase.prototype._getStrokeDashoffset = function (displayPortion) {\n  return this._pathLength * 0.999 * (1 - displayPortion);\n};\n\nStrokeRendererBase.prototype._getColor = function (_ref) {\n  var strokeColor = _ref.strokeColor,\n      radicalColor = _ref.radicalColor;\n\n  return radicalColor && this._stroke.isInRadical ? radicalColor : strokeColor;\n};\n\nmodule.exports = StrokeRendererBase;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nfunction RenderTargetBase() {}\n\nRenderTargetBase.prototype.addPointerStartListener = function (callback) {\n  var _this = this;\n\n  this.node.addEventListener('mousedown', function (evt) {\n    callback(_this._eventify(evt, _this._getMousePoint));\n  });\n  this.node.addEventListener('touchstart', function (evt) {\n    callback(_this._eventify(evt, _this._getTouchPoint));\n  });\n};\n\nRenderTargetBase.prototype.addPointerMoveListener = function (callback) {\n  var _this2 = this;\n\n  this.node.addEventListener('mousemove', function (evt) {\n    callback(_this2._eventify(evt, _this2._getMousePoint));\n  });\n  this.node.addEventListener('touchmove', function (evt) {\n    callback(_this2._eventify(evt, _this2._getTouchPoint));\n  });\n};\n\nRenderTargetBase.prototype.addPointerEndListener = function (callback) {\n  // TODO: find a way to not need global listeners\n  global.document.addEventListener('mouseup', callback);\n  global.document.addEventListener('touchend', callback);\n};\n\nRenderTargetBase.prototype.getBoundingClientRect = function () {\n  return this.node.getBoundingClientRect();\n};\n\nRenderTargetBase.prototype._eventify = function (evt, pointFunc) {\n  var _this3 = this;\n\n  return {\n    getPoint: function getPoint() {\n      return pointFunc.call(_this3, evt);\n    },\n    preventDefault: function preventDefault() {\n      return evt.preventDefault();\n    }\n  };\n};\n\nRenderTargetBase.prototype._getMousePoint = function (evt) {\n  var box = this.getBoundingClientRect();\n  var x = evt.clientX - box.left;\n  var y = evt.clientY - box.top;\n  return { x: x, y: y };\n};\n\nRenderTargetBase.prototype._getTouchPoint = function (evt) {\n  var box = this.getBoundingClientRect();\n  var x = evt.touches[0].clientX - box.left;\n  var y = evt.touches[0].clientY - box.top;\n  return { x: x, y: y };\n};\n\nmodule.exports = RenderTargetBase;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nfunction _toArray(arr) { return Array.isArray(arr) ? arr : Array.from(arr); }\n\nvar drawPath = function drawPath(ctx, points) {\n  ctx.beginPath();\n  var start = points[0];\n  var remainingPoints = points.slice(1);\n  ctx.moveTo(start.x, start.y);\n  remainingPoints.forEach(function (point) {\n    ctx.lineTo(point.x, point.y);\n  });\n  ctx.stroke();\n};\n\n// break a path string into a series of canvas path commands\n// only works with the subset of SVG paths used by MakeMeAHanzi data\nvar pathStringToCanvas = function pathStringToCanvas(pathString) {\n  var pathParts = pathString.split(/(^|\\s+)(?=[A-Z])/).filter(function (part) {\n    return part !== ' ';\n  });\n  var commands = [function (ctx) {\n    return ctx.beginPath();\n  }];\n  pathParts.forEach(function (part) {\n    var _part$split = part.split(/\\s+/),\n        _part$split2 = _toArray(_part$split),\n        cmd = _part$split2[0],\n        rawParams = _part$split2.slice(1);\n\n    var params = rawParams.map(function (param) {\n      return parseFloat(param);\n    });\n    if (cmd === 'M') {\n      commands.push(function (ctx) {\n        return ctx.moveTo.apply(ctx, params);\n      });\n    } else if (cmd === 'L') {\n      commands.push(function (ctx) {\n        return ctx.lineTo.apply(ctx, params);\n      });\n    } else if (cmd === 'C') {\n      commands.push(function (ctx) {\n        return ctx.bezierCurveTo.apply(ctx, params);\n      });\n    } else if (cmd === 'Q') {\n      commands.push(function (ctx) {\n        return ctx.quadraticCurveTo.apply(ctx, params);\n      });\n    } else if (cmd === 'Z') {\n      // commands.push((ctx) => ctx.closePath());\n    }\n  });\n  return function (ctx) {\n    return commands.forEach(function (cmd) {\n      return cmd(ctx);\n    });\n  };\n};\n\nmodule.exports = { drawPath: drawPath, pathStringToCanvas: pathStringToCanvas };\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar RenderState = __webpack_require__(10);\nvar parseCharData = __webpack_require__(11);\nvar Positioner = __webpack_require__(14);\nvar Quiz = __webpack_require__(15);\nvar svgRenderer = __webpack_require__(19);\nvar canvasRenderer = __webpack_require__(25);\nvar defaultCharDataLoader = __webpack_require__(31);\nvar LoadingManager = __webpack_require__(32);\nvar characterActions = __webpack_require__(4);\n\nvar _require = __webpack_require__(0),\n    assign = _require.assign,\n    callIfExists = _require.callIfExists,\n    trim = _require.trim,\n    colorStringToVals = _require.colorStringToVals;\n\nvar defaultOptions = {\n  charDataLoader: defaultCharDataLoader,\n  onLoadCharDataError: null,\n  onLoadCharDataSuccess: null,\n  showOutline: true,\n  showCharacter: true,\n  renderer: 'svg',\n\n  // positioning options\n\n  width: null,\n  height: null,\n  padding: 20,\n\n  // animation options\n\n  strokeAnimationSpeed: 1,\n  strokeFadeDuration: 400,\n  strokeHighlightDuration: 200,\n  strokeHighlightSpeed: 2,\n  delayBetweenStrokes: 1000,\n  delayBetweenLoops: 2000,\n\n  // colors\n\n  strokeColor: '#555',\n  radicalColor: null,\n  highlightColor: '#AAF',\n  outlineColor: '#DDD',\n  drawingColor: '#333',\n\n  // quiz options\n\n  leniency: 1,\n  showHintAfterMisses: 3,\n  highlightOnComplete: true,\n  highlightCompleteColor: null,\n\n  // undocumented obscure options\n\n  drawingFadeDuration: 300,\n  drawingWidth: 4,\n  strokeWidth: 2,\n  outlineWidth: 2,\n  rendererOverride: {}\n};\n\nfunction HanziWriter() {\n  if (arguments.length > 0) {\n    var character = void 0;\n    var options = {};\n    var element = arguments.length <= 0 ? undefined : arguments[0];\n    if (arguments.length > 1) {\n      if (typeof (arguments.length <= 1 ? undefined : arguments[1]) === 'string') {\n        // eslint-disable-next-line\n        console.warn('Using new HanziWriter() to set a character is deprecated. Use HanziWriter.create() instead');\n        character = arguments.length <= 1 ? undefined : arguments[1];\n        options = (arguments.length <= 2 ? undefined : arguments[2]) || {};\n      } else {\n        options = arguments.length <= 1 ? undefined : arguments[1];\n      }\n    }\n    this._init(element, options);\n    if (character) {\n      this.setCharacter(character);\n    }\n  }\n}\n\n// ------ public API ------ //\n\nHanziWriter.prototype.showCharacter = function () {\n  var _this = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this._options.showCharacter = true;\n  return this._withData(function () {\n    return _this._renderState.run(characterActions.showCharacter('main', _this._character, typeof options.duration === 'number' ? options.duration : _this._options.strokeFadeDuration)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\nHanziWriter.prototype.hideCharacter = function () {\n  var _this2 = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this._options.showCharacter = false;\n  return this._withData(function () {\n    return _this2._renderState.run(characterActions.hideCharacter('main', _this2._character, typeof options.duration === 'number' ? options.duration : _this2._options.strokeFadeDuration)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\nHanziWriter.prototype.animateCharacter = function () {\n  var _this3 = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this.cancelQuiz();\n  return this._withData(function () {\n    return _this3._renderState.run(characterActions.animateCharacter('main', _this3._character, _this3._options.strokeFadeDuration, _this3._options.strokeAnimationSpeed, _this3._options.delayBetweenStrokes)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\nHanziWriter.prototype.animateStroke = function (strokeNum) {\n  var _this4 = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  this.cancelQuiz();\n  return this._withData(function () {\n    return _this4._renderState.run(characterActions.animateSingleStroke('main', _this4._character, strokeNum, _this4._options.strokeAnimationSpeed)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\nHanziWriter.prototype.highlightStroke = function (strokeNum) {\n  var _this5 = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  return this._withData(function () {\n    return _this5._renderState.run(characterActions.highlightStroke(_this5._character.strokes[strokeNum], _this5._options.highlightColor, _this5._options.strokeHighlightSpeed)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\nHanziWriter.prototype.loopCharacterAnimation = function () {\n  var _this6 = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this.cancelQuiz();\n  return this._withData(function () {\n    return _this6._renderState.run(characterActions.animateCharacterLoop('main', _this6._character, _this6._options.strokeFadeDuration, _this6._options.strokeAnimationSpeed, _this6._options.delayBetweenStrokes, _this6._options.delayBetweenLoops), { loop: true });\n  });\n};\n\nHanziWriter.prototype.pauseAnimation = function () {\n  var _this7 = this;\n\n  return this._withData(function () {\n    return _this7._renderState.pauseAll();\n  });\n};\n\nHanziWriter.prototype.resumeAnimation = function () {\n  var _this8 = this;\n\n  return this._withData(function () {\n    return _this8._renderState.resumeAll();\n  });\n};\n\nHanziWriter.prototype.showOutline = function () {\n  var _this9 = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this._options.showOutline = true;\n  return this._withData(function () {\n    return _this9._renderState.run(characterActions.showCharacter('outline', _this9._character, typeof options.duration === 'number' ? options.duration : _this9._options.strokeFadeDuration)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\n\nHanziWriter.prototype.hideOutline = function () {\n  var _this10 = this;\n\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this._options.showOutline = false;\n  return this._withData(function () {\n    return _this10._renderState.run(characterActions.hideCharacter('outline', _this10._character, typeof options.duration === 'number' ? options.duration : _this10._options.strokeFadeDuration)).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\n\nHanziWriter.prototype.updateColor = function (colorName, colorVal) {\n  var _this11 = this;\n\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  return this._withData(function () {\n    var duration = typeof options.duration === 'number' ? options.duration : _this11._options.strokeFadeDuration;\n    var fixedColorVal = colorVal;\n    // If we're removing radical color, tween it to the stroke color\n    if (colorName === 'radicalColor' && !colorVal) {\n      fixedColorVal = _this11._options.strokeColor;\n    }\n    var mappedColor = colorStringToVals(fixedColorVal);\n    _this11._options[colorName] = colorVal;\n    var mutation = characterActions.updateColor(colorName, mappedColor, duration);\n    // make sure to set radicalColor back to null after the transition finishes if val == null\n    if (colorName === 'radicalColor' && !colorVal) {\n      mutation = mutation.concat(characterActions.updateColor(colorName, null, 0));\n    }\n    return _this11._renderState.run(mutation).then(function (res) {\n      return callIfExists(options.onComplete, res);\n    });\n  });\n};\n\nHanziWriter.prototype.quiz = function () {\n  var _this12 = this;\n\n  var quizOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  this._withData(function () {\n    _this12.cancelQuiz();\n    _this12._quiz = new Quiz(_this12._character, _this12._renderState, _this12._positioner);\n    _this12._quiz.startQuiz(assign({}, _this12._options, quizOptions));\n  });\n};\n\nHanziWriter.prototype.cancelQuiz = function () {\n  if (this._quiz) {\n    this._quiz.cancel();\n    this._quiz = null;\n  }\n};\n\nHanziWriter.prototype.setCharacter = function (char) {\n  var _this13 = this;\n\n  this.cancelQuiz();\n  this._char = char;\n  if (this._hanziWriterRenderer) this._hanziWriterRenderer.destroy();\n  if (this._renderState) this._renderState.cancelAll();\n  this._hanziWriterRenderer = null;\n  this._withDataPromise = this._loadingManager.loadCharData(char).then(function (pathStrings) {\n    if (_this13._loadingManager.loadingFailed) return;\n\n    _this13._character = parseCharData(char, pathStrings);\n    _this13._positioner = new Positioner(_this13._options);\n    var hanziWriterRenderer = new _this13._renderer.HanziWriterRenderer(_this13._character, _this13._positioner);\n    _this13._hanziWriterRenderer = hanziWriterRenderer;\n    _this13._renderState = new RenderState(_this13._character, _this13._options, function (nextState) {\n      hanziWriterRenderer.render(nextState);\n    });\n    _this13._hanziWriterRenderer.mount(_this13.target);\n    _this13._hanziWriterRenderer.render(_this13._renderState.state);\n  });\n  return this._withDataPromise;\n};\n\n// ------------- //\n\nHanziWriter.prototype._init = function (element, options) {\n  var renderer = options.renderer === 'canvas' ? canvasRenderer : svgRenderer;\n  var rendererOverride = options.rendererOverride || {};\n  this._renderer = {\n    HanziWriterRenderer: rendererOverride.HanziWriterRenderer || renderer.HanziWriterRenderer,\n    createRenderTarget: rendererOverride.createRenderTarget || renderer.createRenderTarget\n  };\n  // wechat miniprogram component needs direct access to the render target, so this is public\n  this.target = this._renderer.createRenderTarget(element, options.width, options.height);\n  this._options = this._assignOptions(options);\n  this._loadingManager = new LoadingManager(this._options);\n  this._setupListeners();\n  this._quiz = null;\n  return this;\n};\n\nHanziWriter.prototype._assignOptions = function (options) {\n  var mergedOptions = assign({}, defaultOptions, options);\n\n  // backfill strokeAnimationSpeed if deprecated strokeAnimationDuration is provided instead\n  if (options.strokeAnimationDuration && !options.strokeAnimationSpeed) {\n    mergedOptions.strokeAnimationSpeed = 500 / mergedOptions.strokeAnimationDuration;\n  }\n  if (options.strokeHighlightDuration && !options.strokeHighlightSpeed) {\n    mergedOptions.strokeHighlightSpeed = 500 / mergedOptions.strokeHighlightDuration;\n  }\n\n  if (!options.highlightCompleteColor) {\n    mergedOptions.highlightCompleteColor = mergedOptions.highlightColor;\n  }\n\n  return this._fillWidthAndHeight(mergedOptions);\n};\n\n// returns a new options object with width and height filled in if missing\nHanziWriter.prototype._fillWidthAndHeight = function (options) {\n  var filledOpts = assign({}, options);\n  if (filledOpts.width && !filledOpts.height) {\n    filledOpts.height = filledOpts.width;\n  } else if (filledOpts.height && !filledOpts.width) {\n    filledOpts.width = filledOpts.height;\n  } else if (!filledOpts.width && !filledOpts.height) {\n    var _target$getBoundingCl = this.target.getBoundingClientRect(),\n        width = _target$getBoundingCl.width,\n        height = _target$getBoundingCl.height;\n\n    var minDim = Math.min(width, height);\n    filledOpts.width = minDim;\n    filledOpts.height = minDim;\n  }\n  return filledOpts;\n};\n\nHanziWriter.prototype._withData = function (func) {\n  var _this14 = this;\n\n  // if this._loadingManager.loadingFailed, then loading failed before this method was called\n  if (this._loadingManager.loadingFailed) {\n    throw Error('Failed to load character data. Call setCharacter and try again.');\n  }\n  return this._withDataPromise.then(function () {\n    if (!_this14._loadingManager.loadingFailed) {\n      return func();\n    }\n  });\n};\n\nHanziWriter.prototype._setupListeners = function () {\n  var _this15 = this;\n\n  this.target.addPointerStartListener(function (evt) {\n    if (!_this15._quiz) return;\n    evt.preventDefault();\n    _this15._forwardToQuiz('startUserStroke', evt.getPoint());\n  });\n  this.target.addPointerMoveListener(function (evt) {\n    if (!_this15._quiz) return;\n    evt.preventDefault();\n    _this15._forwardToQuiz('continueUserStroke', evt.getPoint());\n  });\n  this.target.addPointerEndListener(function () {\n    return _this15._forwardToQuiz('endUserStroke');\n  });\n};\n\nHanziWriter.prototype._forwardToQuiz = function (method) {\n  var _quiz;\n\n  if (!this._quiz) return;\n\n  for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  (_quiz = this._quiz)[method].apply(_quiz, args);\n};\n\n// --- Static Public API --- //\n\nHanziWriter.create = function (element, character) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  var writer = new HanziWriter(element, options);\n  writer.setCharacter(character);\n  return writer;\n};\n\nvar lastLoadingManager = null;\nvar lastLoadingOptions = null;\n\nHanziWriter.loadCharacterData = function (character) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var loadingManager = void 0;\n  if (lastLoadingManager && lastLoadingOptions === options) {\n    loadingManager = lastLoadingManager;\n  } else {\n    loadingManager = new LoadingManager(assign({}, defaultOptions, options));\n  }\n  lastLoadingManager = loadingManager;\n  lastLoadingOptions = options;\n  return loadingManager.loadCharData(character);\n};\n\nHanziWriter.getScalingTransform = function (width, height) {\n  var padding = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n\n  var positioner = new Positioner({ width: width, height: height, padding: padding });\n  return {\n    x: positioner.xOffset,\n    y: positioner.yOffset,\n    scale: positioner.scale,\n    transform: trim('\\n      translate(' + positioner.xOffset + ', ' + (positioner.height - positioner.yOffset) + ')\\n      scale(' + positioner.scale + ', ' + -1 * positioner.scale + ')\\n    ').replace(/\\s+/g, ' ')\n  };\n};\n\nmodule.exports = HanziWriter;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    copyAndMergeDeep = _require.copyAndMergeDeep,\n    colorStringToVals = _require.colorStringToVals;\n\nfunction RenderState(character, options, onStateChange) {\n  this._onStateChange = onStateChange;\n  this._mutationChains = [];\n  this.state = {\n    options: {\n      drawingFadeDuration: options.drawingFadeDuration,\n      drawingWidth: options.drawingWidth,\n      drawingColor: colorStringToVals(options.drawingColor),\n      strokeColor: colorStringToVals(options.strokeColor),\n      outlineColor: colorStringToVals(options.outlineColor),\n      radicalColor: colorStringToVals(options.radicalColor || options.strokeColor),\n      highlightColor: colorStringToVals(options.highlightColor)\n    },\n    character: {\n      main: {\n        opacity: options.showCharacter ? 1 : 0,\n        strokes: {}\n      },\n      outline: {\n        opacity: options.showOutline ? 1 : 0,\n        strokes: {}\n      },\n      highlight: {\n        opacity: 1,\n        strokes: {}\n      }\n    },\n    userStrokes: null\n  };\n  for (var i = 0; i < character.strokes.length; i++) {\n    this.state.character.main.strokes[i] = {\n      opacity: 1,\n      displayPortion: 1\n    };\n    this.state.character.outline.strokes[i] = {\n      opacity: 1,\n      displayPortion: 1\n    };\n    this.state.character.highlight.strokes[i] = {\n      opacity: 0,\n      displayPortion: 1\n    };\n  }\n}\n\nRenderState.prototype.updateState = function (stateChanges) {\n  var nextState = copyAndMergeDeep(this.state, stateChanges);\n  this._onStateChange(nextState, this.state);\n  this.state = nextState;\n};\n\nRenderState.prototype.run = function (mutations) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var scopes = mutations.map(function (mut) {\n    return mut.scope;\n  }).filter(function (x) {\n    return x;\n  });\n  this.cancelMutations(scopes);\n  return new Promise(function (resolve) {\n    var mutationChain = {\n      _isActive: true,\n      _index: 0,\n      _resolve: resolve,\n      _mutations: mutations,\n      _loop: options.loop,\n      _scopes: scopes\n    };\n    _this._mutationChains.push(mutationChain);\n    _this._run(mutationChain);\n  });\n};\n\nRenderState.prototype._run = function (mutationChain) {\n  var _this2 = this;\n\n  if (!mutationChain._isActive) return;\n  var mutations = mutationChain._mutations;\n  if (mutationChain._index >= mutations.length) {\n    if (mutationChain._loop) {\n      mutationChain._index = 0; // eslint-disable-line no-param-reassign\n    } else {\n      mutationChain._isActive = false; // eslint-disable-line no-param-reassign\n      this._mutationChains = this._mutationChains.filter(function (chain) {\n        return chain !== mutationChain;\n      });\n      // The chain is done - resolve the promise to signal it finished successfully\n      mutationChain._resolve({ canceled: false });\n      return;\n    }\n  }\n\n  var activeMutation = mutationChain._mutations[mutationChain._index];\n  activeMutation.run(this).then(function () {\n    if (mutationChain._isActive) {\n      mutationChain._index++; // eslint-disable-line no-param-reassign\n      _this2._run(mutationChain);\n    }\n  });\n};\n\nRenderState.prototype._getActiveMutations = function () {\n  return this._mutationChains.map(function (chain) {\n    return chain._mutations[chain._index];\n  });\n};\n\nRenderState.prototype.pauseAll = function () {\n  this._getActiveMutations().forEach(function (mutation) {\n    return mutation.pause();\n  });\n};\n\nRenderState.prototype.resumeAll = function () {\n  this._getActiveMutations().forEach(function (mutation) {\n    return mutation.resume();\n  });\n};\n\nRenderState.prototype.cancelMutations = function (scopes) {\n  var _this3 = this;\n\n  this._mutationChains.forEach(function (chain) {\n    chain._scopes.forEach(function (chainScope) {\n      scopes.forEach(function (scope) {\n        if (chainScope.indexOf(scope) >= 0 || scope.indexOf(chainScope) >= 0) {\n          _this3._cancelMutationChain(chain);\n        }\n      });\n    });\n  });\n};\n\nRenderState.prototype.cancelAll = function () {\n  this.cancelMutations(['']);\n};\n\nRenderState.prototype._cancelMutationChain = function (mutationChain) {\n  mutationChain._isActive = false; // eslint-disable-line no-param-reassign\n  for (var i = mutationChain._index; i < mutationChain._mutations.length; i++) {\n    mutationChain._mutations[i].cancel(this);\n  }\n  if (mutationChain._resolve) {\n    mutationChain._resolve({ canceled: true });\n  }\n  this._mutationChains = this._mutationChains.filter(function (chain) {\n    return chain !== mutationChain;\n  });\n};\n\nmodule.exports = RenderState;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar Stroke = __webpack_require__(12);\nvar Character = __webpack_require__(13);\n\nvar generateStrokes = function generateStrokes(charJson) {\n  var isInRadical = function isInRadical(strokeNum) {\n    return charJson.radStrokes && charJson.radStrokes.indexOf(strokeNum) >= 0;\n  };\n\n  return charJson.strokes.map(function (path, index) {\n    var points = charJson.medians[index].map(function (pointData) {\n      var _pointData = _slicedToArray(pointData, 2),\n          x = _pointData[0],\n          y = _pointData[1];\n\n      return { x: x, y: y };\n    });\n    return new Stroke(path, points, index, isInRadical(index));\n  });\n};\n\nvar parseCharData = function parseCharData(symbol, charJson) {\n  var strokes = generateStrokes(charJson);\n  return new Character(symbol, strokes);\n};\n\nmodule.exports = parseCharData;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(2),\n    subtract = _require.subtract,\n    distance = _require.distance,\n    length = _require.length;\n\nfunction Stroke(path, points, strokeNum) {\n  var isInRadical = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n  this.path = path;\n  this.points = points;\n  this.strokeNum = strokeNum;\n  this.isInRadical = isInRadical;\n}\n\nStroke.prototype.getStartingPoint = function () {\n  return this.points[0];\n};\n\nStroke.prototype.getEndingPoint = function () {\n  return this.points[this.points.length - 1];\n};\n\nStroke.prototype.getLength = function () {\n  return length(this.points);\n};\n\nStroke.prototype.getVectors = function () {\n  var lastPoint = this.points[0];\n  var pointsSansFirst = this.points.slice(1);\n  return pointsSansFirst.map(function (point) {\n    var vector = subtract(point, lastPoint);\n    lastPoint = point;\n    return vector;\n  });\n};\n\nStroke.prototype.getDistance = function (point) {\n  var distances = this.points.map(function (strokePoint) {\n    return distance(strokePoint, point);\n  });\n  return Math.min.apply(Math, distances);\n};\n\nStroke.prototype.getAverageDistance = function (points) {\n  var _this = this;\n\n  var totalDist = points.reduce(function (acc, point) {\n    return acc + _this.getDistance(point);\n  }, 0);\n  return totalDist / points.length;\n};\n\nmodule.exports = Stroke;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nfunction Character(symbol, strokes) {\n  this.symbol = symbol;\n  this.strokes = strokes;\n}\n\nmodule.exports = Character;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\n// All makemeahanzi characters have the same bounding box\nvar CHARACTER_BOUNDS = [{ x: 0, y: -124 }, { x: 1024, y: 900 }];\n\nfunction Positioner(options) {\n  this._options = options;\n  this.width = options.width;\n  this.height = options.height;\n  this._calculateScaleAndOffset();\n}\n\nPositioner.prototype.convertExternalPoint = function (point) {\n  var x = (point.x - this.xOffset) / this.scale;\n  var y = (this.height - this.yOffset - point.y) / this.scale;\n  return { x: x, y: y };\n};\n\nPositioner.prototype._calculateScaleAndOffset = function () {\n  var bounds = CHARACTER_BOUNDS;\n  var preScaledWidth = bounds[1].x - bounds[0].x;\n  var preScaledHeight = bounds[1].y - bounds[0].y;\n  var effectiveWidth = this.width - 2 * this._options.padding;\n  var effectiveHeight = this.height - 2 * this._options.padding;\n  var scaleX = effectiveWidth / preScaledWidth;\n  var scaleY = effectiveHeight / preScaledHeight;\n\n  this.scale = Math.min(scaleX, scaleY);\n\n  var xCenteringBuffer = this._options.padding + (effectiveWidth - this.scale * preScaledWidth) / 2;\n  var yCenteringBuffer = this._options.padding + (effectiveHeight - this.scale * preScaledHeight) / 2;\n\n  this.xOffset = -1 * bounds[0].x * this.scale + xCenteringBuffer;\n  this.yOffset = -1 * bounds[0].y * this.scale + yCenteringBuffer;\n};\n\nmodule.exports = Positioner;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar strokeMatches = __webpack_require__(16);\nvar UserStroke = __webpack_require__(17);\n\nvar _require = __webpack_require__(0),\n    callIfExists = _require.callIfExists,\n    counter = _require.counter;\n\nvar quizActions = __webpack_require__(18);\nvar geometry = __webpack_require__(2);\nvar characterActions = __webpack_require__(4);\n\nvar getDrawnPath = function getDrawnPath(userStroke) {\n  return {\n    pathString: geometry.getPathString(userStroke.externalPoints),\n    points: userStroke.points.map(function (point) {\n      return geometry.round(point);\n    })\n  };\n};\n\nfunction Quiz(character, renderState, positioner) {\n  this._character = character;\n  this._renderState = renderState;\n  this._isActive = false;\n  this._positioner = positioner;\n}\n\nQuiz.prototype.startQuiz = function (options) {\n  this._isActive = true;\n  this._options = options;\n  this._currentStrokeIndex = 0;\n  this._numRecentMistakes = 0;\n  this._totalMistakes = 0;\n  this._renderState.run(quizActions.startQuiz(this._character, options.strokeFadeDuration));\n};\n\nQuiz.prototype.startUserStroke = function (externalPoint) {\n  var point = this._positioner.convertExternalPoint(externalPoint);\n  if (!this._isActive) return null;\n  if (this._userStroke) return this.endUserStroke();\n  var strokeId = counter();\n  this._userStroke = new UserStroke(strokeId, point, externalPoint);\n  this._renderState.run(quizActions.startUserStroke(strokeId, point));\n};\n\nQuiz.prototype.continueUserStroke = function (externalPoint) {\n  if (!this._userStroke) return;\n  var point = this._positioner.convertExternalPoint(externalPoint);\n  this._userStroke.appendPoint(point, externalPoint);\n  var nextPoints = this._userStroke.points.slice(0);\n  this._renderState.run(quizActions.updateUserStroke(this._userStroke.id, nextPoints));\n};\n\nQuiz.prototype.endUserStroke = function () {\n  if (!this._userStroke) return;\n\n  this._renderState.run(quizActions.removeUserStroke(this._userStroke.id, this._options.drawingFadeDuration));\n  // skip single-point strokes\n  if (this._userStroke.points.length === 1) {\n    this._userStroke = null;\n    return;\n  }\n\n  var currentStroke = this._getCurrentStroke();\n  var isOutlineVisible = this._renderState.state.character.outline.opacity > 0;\n  var isMatch = strokeMatches(this._userStroke, this._character, this._currentStrokeIndex, {\n    isOutlineVisible: isOutlineVisible,\n    leniency: this._options.leniency\n  });\n\n  if (isMatch) {\n    this._handleSuccess();\n  } else {\n    this._handleFailure();\n    if (this._numRecentMistakes >= this._options.showHintAfterMisses && this._options.showHintAfterMisses !== false) {\n      this._renderState.run(quizActions.highlightStroke(currentStroke, this._options.highlightColor, this._options.strokeHighlightSpeed));\n    }\n  }\n  this._userStroke = null;\n};\n\nQuiz.prototype.cancel = function () {\n  this._isActive = false;\n  if (this._userStroke) {\n    this._renderState.run(quizActions.removeUserStroke(this._userStroke.id, this._options.drawingFadeDuration));\n  }\n};\n\nQuiz.prototype._handleSuccess = function () {\n  callIfExists(this._options.onCorrectStroke, {\n    character: this._character.symbol,\n    strokeNum: this._currentStrokeIndex,\n    mistakesOnStroke: this._numRecentMistakes,\n    totalMistakes: this._totalMistakes,\n    strokesRemaining: this._character.strokes.length - this._currentStrokeIndex - 1,\n    drawnPath: getDrawnPath(this._userStroke)\n  });\n  var animation = characterActions.showStroke('main', this._currentStrokeIndex, this._options.strokeFadeDuration);\n  this._currentStrokeIndex += 1;\n  this._numRecentMistakes = 0;\n\n  if (this._currentStrokeIndex === this._character.strokes.length) {\n    this._isActive = false;\n    callIfExists(this._options.onComplete, {\n      character: this._character.symbol,\n      totalMistakes: this._totalMistakes\n    });\n    if (this._options.highlightOnComplete) {\n      animation = animation.concat(quizActions.highlightCompleteChar(this._character, this._options.highlightCompleteColor, this._options.strokeHighlightDuration * 2));\n    }\n  }\n  this._renderState.run(animation);\n};\n\nQuiz.prototype._handleFailure = function () {\n  this._numRecentMistakes += 1;\n  this._totalMistakes += 1;\n  callIfExists(this._options.onMistake, {\n    character: this._character.symbol,\n    strokeNum: this._currentStrokeIndex,\n    mistakesOnStroke: this._numRecentMistakes,\n    totalMistakes: this._totalMistakes,\n    strokesRemaining: this._character.strokes.length - this._currentStrokeIndex,\n    drawnPath: getDrawnPath(this._userStroke)\n  });\n};\n\nQuiz.prototype._getCurrentStroke = function () {\n  return this._character.strokes[this._currentStrokeIndex];\n};\n\nmodule.exports = Quiz;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    average = _require.average,\n    assign = _require.assign;\n\nvar _require2 = __webpack_require__(2),\n    cosineSimilarity = _require2.cosineSimilarity,\n    equals = _require2.equals,\n    frechetDist = _require2.frechetDist,\n    distance = _require2.distance,\n    subtract = _require2.subtract,\n    normalizeCurve = _require2.normalizeCurve,\n    rotate = _require2.rotate,\n    length = _require2.length;\n\nvar AVG_DIST_THRESHOLD = 350; // bigger = more lenient\nvar COSINE_SIMILARITY_THRESHOLD = 0; // -1 to 1, smaller = more lenient\nvar START_AND_END_DIST_THRESHOLD = 250; // bigger = more lenient\nvar FRECHET_THRESHOLD = 0.40; // bigger = more lenient\nvar MIN_LEN_THRESHOLD = 0.35; // smaller = more lenient\n\nvar startAndEndMatches = function startAndEndMatches(points, closestStroke, leniency) {\n  var startingDist = distance(closestStroke.getStartingPoint(), points[0]);\n  var endingDist = distance(closestStroke.getEndingPoint(), points[points.length - 1]);\n  return startingDist <= START_AND_END_DIST_THRESHOLD * leniency && endingDist <= START_AND_END_DIST_THRESHOLD * leniency;\n};\n\n// returns a list of the direction of all segments in the line connecting the points\nvar getEdgeVectors = function getEdgeVectors(points) {\n  var vectors = [];\n  var lastPoint = points[0];\n  points.slice(1).forEach(function (point) {\n    vectors.push(subtract(point, lastPoint));\n    lastPoint = point;\n  });\n  return vectors;\n};\n\nvar directionMatches = function directionMatches(points, stroke) {\n  var edgeVectors = getEdgeVectors(points);\n  var strokeVectors = stroke.getVectors();\n  var similarities = edgeVectors.map(function (edgeVector) {\n    var strokeSimilarities = strokeVectors.map(function (strokeVector) {\n      return cosineSimilarity(strokeVector, edgeVector);\n    });\n    return Math.max.apply(Math, strokeSimilarities);\n  });\n  var avgSimilarity = average(similarities);\n  return avgSimilarity > COSINE_SIMILARITY_THRESHOLD;\n};\n\nvar lengthMatches = function lengthMatches(points, stroke, leniency) {\n  return leniency * (length(points) + 25) / (stroke.getLength() + 25) >= MIN_LEN_THRESHOLD;\n};\n\nvar stripDuplicates = function stripDuplicates(points) {\n  if (points.length < 2) return points;\n  var dedupedPoints = [points[0]];\n  points.slice(1).forEach(function (point) {\n    if (!equals(point, dedupedPoints[dedupedPoints.length - 1])) {\n      dedupedPoints.push(point);\n    }\n  });\n  return dedupedPoints;\n};\n\nvar SHAPE_FIT_ROTATIONS = [Math.PI / 16, Math.PI / 32, 0, -1 * Math.PI / 32, -1 * Math.PI / 16];\n\nvar shapeFit = function shapeFit(curve1, curve2, leniency) {\n  var normCurve1 = normalizeCurve(curve1);\n  var normCurve2 = normalizeCurve(curve2);\n  var minDist = Infinity;\n  SHAPE_FIT_ROTATIONS.forEach(function (theta) {\n    var dist = frechetDist(normCurve1, rotate(normCurve2, theta));\n    if (dist < minDist) {\n      minDist = dist;\n    }\n  });\n  return minDist <= FRECHET_THRESHOLD * leniency;\n};\n\nvar getMatchData = function getMatchData(points, stroke, options) {\n  var _options$leniency = options.leniency,\n      leniency = _options$leniency === undefined ? 1 : _options$leniency,\n      _options$isOutlineVis = options.isOutlineVisible,\n      isOutlineVisible = _options$isOutlineVis === undefined ? false : _options$isOutlineVis;\n\n  var avgDist = stroke.getAverageDistance(points);\n  var distMod = isOutlineVisible || stroke.strokeNum > 0 ? 0.5 : 1;\n  var withinDistThresh = avgDist <= AVG_DIST_THRESHOLD * distMod * leniency;\n  // short circuit for faster matching\n  if (!withinDistThresh) {\n    return { isMatch: false, avgDist: avgDist };\n  }\n  var startAndEndMatch = startAndEndMatches(points, stroke, leniency);\n  var directionMatch = directionMatches(points, stroke);\n  var shapeMatch = shapeFit(points, stroke.points, leniency);\n  var lengthMatch = lengthMatches(points, stroke, leniency);\n  return {\n    isMatch: withinDistThresh && startAndEndMatch && directionMatch && shapeMatch && lengthMatch,\n    avgDist: avgDist\n  };\n};\n\nvar strokeMatches = function strokeMatches(userStroke, character, strokeNum) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n\n  var points = stripDuplicates(userStroke.points);\n  if (points.length < 2) return null;\n\n  var strokeMatchData = getMatchData(points, character.strokes[strokeNum], options);\n  if (!strokeMatchData.isMatch) return false;\n\n  // if there is a better match among strokes the user hasn't drawn yet, the user probably drew the wrong stroke\n  var laterStrokes = character.strokes.slice(strokeNum + 1);\n  var closestMatchDist = strokeMatchData.avgDist;\n  for (var i = 0; i < laterStrokes.length; i++) {\n    var laterMatchData = getMatchData(points, laterStrokes[i], options);\n    if (laterMatchData.isMatch && laterMatchData.avgDist < closestMatchDist) {\n      closestMatchDist = laterMatchData.avgDist;\n    }\n  }\n  // if there's a better match, rather that returning false automatically, try reducing leniency instead\n  // if leniency is already really high we can allow some similar strokes to pass\n  if (closestMatchDist < strokeMatchData.avgDist) {\n    // adjust leniency between 0.3 and 0.6 depending on how much of a better match the new match is\n    var leniencyAdjustment = 0.6 * (closestMatchDist + strokeMatchData.avgDist) / (2 * strokeMatchData.avgDist);\n    var newLeniency = (options.leniency || 1) * leniencyAdjustment;\n    var adjustedOptions = assign({}, options, { leniency: newLeniency });\n    var adjustedStrokeMatchData = getMatchData(points, character.strokes[strokeNum], adjustedOptions);\n    return adjustedStrokeMatchData.isMatch;\n  }\n  return true;\n};\n\nmodule.exports = strokeMatches;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nfunction UserStroke(id, startingPoint, startingExternalPoint) {\n  this.id = id;\n  this.points = [startingPoint];\n  this.externalPoints = [startingExternalPoint];\n}\n\nUserStroke.prototype.appendPoint = function (point, externalPoint) {\n  this.points.push(point);\n  this.externalPoints.push(externalPoint);\n};\n\nmodule.exports = UserStroke;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar Mutation = __webpack_require__(5);\nvar characterActions = __webpack_require__(4);\n\nvar _require = __webpack_require__(0),\n    objRepeat = _require.objRepeat;\n\nvar startQuiz = function startQuiz(character, fadeDuration) {\n  return characterActions.hideCharacter('main', character, fadeDuration).concat([new Mutation('character.highlight', {\n    opacity: 1,\n    strokes: objRepeat({ opacity: 0 }, character.strokes.length)\n  }, { force: true }), new Mutation('character.main', {\n    opacity: 1,\n    strokes: objRepeat({ opacity: 0 }, character.strokes.length)\n  }, { force: true })]);\n};\n\nvar startUserStroke = function startUserStroke(id, point) {\n  return [new Mutation('quiz.activeUserStrokeId', id, { force: true }), new Mutation('userStrokes.' + id, {\n    points: [point],\n    opacity: 1\n  }, { force: true })];\n};\n\nvar updateUserStroke = function updateUserStroke(userStrokeId, points) {\n  return [new Mutation('userStrokes.' + userStrokeId + '.points', points, { force: true })];\n};\n\nvar removeUserStroke = function removeUserStroke(userStrokeId, duration) {\n  return [new Mutation('userStrokes.' + userStrokeId + '.opacity', 0, { duration: duration }), new Mutation('userStrokes.' + userStrokeId, null, { force: true })];\n};\n\nvar highlightCompleteChar = function highlightCompleteChar(character, color, duration) {\n  return [new Mutation('character.highlight.strokeColor', color)].concat(characterActions.hideCharacter('highlight', character)).concat(characterActions.showCharacter('highlight', character, duration / 2)).concat(characterActions.hideCharacter('highlight', character, duration / 2));\n};\n\nmodule.exports = {\n  highlightCompleteChar: highlightCompleteChar,\n  highlightStroke: characterActions.highlightStroke,\n  startQuiz: startQuiz,\n  startUserStroke: startUserStroke,\n  updateUserStroke: updateUserStroke,\n  removeUserStroke: removeUserStroke\n};\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar HanziWriterRenderer = __webpack_require__(20);\nvar RenderTarget = __webpack_require__(24);\n\nmodule.exports = {\n  HanziWriterRenderer: HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init\n};\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar CharacterRenderer = __webpack_require__(21);\nvar UserStrokeRenderer = __webpack_require__(23);\n\nvar _require = __webpack_require__(0),\n    assign = _require.assign;\n\nvar svg = __webpack_require__(3);\n\nfunction HanziWriterRenderer(character, positioner) {\n  this._character = character;\n  this._positioner = positioner;\n  this._mainCharRenderer = new CharacterRenderer(character);\n  this._outlineCharRenderer = new CharacterRenderer(character);\n  this._highlightCharRenderer = new CharacterRenderer(character);\n  this._userStrokeRenderers = {};\n}\n\nHanziWriterRenderer.prototype.mount = function (target) {\n  var positionedTarget = target.createSubRenderTarget();\n  var group = positionedTarget.svg;\n  svg.attr(group, 'transform', '\\n    translate(' + this._positioner.xOffset + ', ' + (this._positioner.height - this._positioner.yOffset) + ')\\n    scale(' + this._positioner.scale + ', ' + -1 * this._positioner.scale + ')\\n  ');\n  this._outlineCharRenderer.mount(positionedTarget);\n  this._mainCharRenderer.mount(positionedTarget);\n  this._highlightCharRenderer.mount(positionedTarget);\n  this._positionedTarget = positionedTarget;\n};\n\nHanziWriterRenderer.prototype.render = function (props) {\n  var _this = this;\n\n  this._outlineCharRenderer.render({\n    opacity: props.character.outline.opacity,\n    strokes: props.character.outline.strokes,\n    strokeColor: props.options.outlineColor\n  });\n  this._mainCharRenderer.render({\n    opacity: props.character.main.opacity,\n    strokes: props.character.main.strokes,\n    strokeColor: props.options.strokeColor,\n    radicalColor: props.options.radicalColor\n  });\n  this._highlightCharRenderer.render({\n    opacity: props.character.highlight.opacity,\n    strokes: props.character.highlight.strokes,\n    strokeColor: props.options.highlightColor\n  });\n\n  var userStrokes = props.userStrokes || {};\n  Object.keys(this._userStrokeRenderers).forEach(function (userStrokeId) {\n    if (!userStrokes[userStrokeId]) {\n      _this._userStrokeRenderers[userStrokeId].destroy();\n      delete _this._userStrokeRenderers[userStrokeId];\n    }\n  });\n\n  Object.keys(userStrokes).forEach(function (userStrokeId) {\n    if (!userStrokes[userStrokeId]) return;\n    var userStrokeProps = assign({\n      strokeWidth: props.options.drawingWidth,\n      strokeColor: props.options.drawingColor\n    }, userStrokes[userStrokeId]);\n    var strokeRenderer = _this._userStrokeRenderers[userStrokeId];\n    if (!strokeRenderer) {\n      strokeRenderer = new UserStrokeRenderer();\n      strokeRenderer.mount(_this._positionedTarget);\n      _this._userStrokeRenderers[userStrokeId] = strokeRenderer;\n    }\n    strokeRenderer.render(userStrokeProps);\n  });\n};\n\nHanziWriterRenderer.prototype.destroy = function () {\n  svg.removeElm(this._positionedTarget.svg);\n  this._positionedTarget.defs.innerHTML = '';\n};\n\nmodule.exports = HanziWriterRenderer;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    isMsBrowser = _require.isMsBrowser;\n\nvar StrokeRenderer = __webpack_require__(22);\n\nfunction CharacterRenderer(character) {\n  this._oldProps = {};\n  this._strokeRenderers = character.strokes.map(function (stroke) {\n    return new StrokeRenderer(stroke);\n  });\n}\n\nCharacterRenderer.prototype.mount = function (target) {\n  var subTarget = target.createSubRenderTarget();\n  this._group = subTarget.svg;\n  this._strokeRenderers.forEach(function (strokeRenderer, i) {\n    strokeRenderer.mount(subTarget);\n  });\n};\n\nCharacterRenderer.prototype.render = function (props) {\n  if (props === this._oldProps) return;\n  if (props.opacity !== this._oldProps.opacity) {\n    this._group.style.opacity = props.opacity;\n    // MS browsers seem to have a bug where if SVG is set to display:none, it sometimes breaks.\n    // More info: https://github.com/chanind/hanzi-writer/issues/164\n    // this is just a perf improvement, so disable for MS browsers\n    if (!isMsBrowser) {\n      if (props.opacity === 0) {\n        this._group.style.display = 'none';\n      } else if (this._oldProps.opacity === 0) {\n        this._group.style.removeProperty('display');\n      }\n    }\n  }\n  var colorsChanged = !this._oldProps || props.strokeColor !== this._oldProps.strokeColor || props.radicalColor !== this._oldProps.radicalColor;\n  if (colorsChanged || props.strokes !== this._oldProps.strokes) {\n    for (var i = 0; i < this._strokeRenderers.length; i++) {\n      if (!colorsChanged && this._oldProps.strokes && props.strokes[i] === this._oldProps.strokes[i]) continue;\n      this._strokeRenderers[i].render({\n        strokeColor: props.strokeColor,\n        radicalColor: props.radicalColor,\n        opacity: props.strokes[i].opacity,\n        displayPortion: props.strokes[i].displayPortion\n      });\n    }\n  }\n  this._oldProps = props;\n};\n\nmodule.exports = CharacterRenderer;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    counter = _require.counter;\n\nvar svg = __webpack_require__(3);\n\nvar _require2 = __webpack_require__(2),\n    extendStart = _require2.extendStart,\n    getPathString = _require2.getPathString;\n\nvar StrokeRendererBase = __webpack_require__(6);\n\nvar STROKE_WIDTH = 200;\n\n// this is a stroke composed of several stroke parts\nfunction StrokeRenderer(stroke) {\n  this._oldProps = {};\n  this._stroke = stroke;\n  this._pathLength = stroke.getLength() + STROKE_WIDTH / 2;\n}\nStrokeRenderer.prototype = Object.create(StrokeRendererBase.prototype);\n\nStrokeRenderer.prototype.mount = function (target) {\n  this._animationPath = svg.createElm('path');\n  this._clip = svg.createElm('clipPath');\n  this._strokePath = svg.createElm('path');\n  var maskId = 'mask-' + counter();\n  svg.attr(this._clip, 'id', maskId);\n\n  svg.attr(this._strokePath, 'd', this._stroke.path);\n  this._animationPath.style.opacity = 0;\n  svg.attr(this._animationPath, 'clip-path', svg.urlIdRef(maskId));\n\n  var extendedMaskPoints = extendStart(this._stroke.points, STROKE_WIDTH / 2);\n  svg.attr(this._animationPath, 'd', getPathString(extendedMaskPoints));\n  svg.attrs(this._animationPath, {\n    stroke: '#FFFFFF',\n    'stroke-width': STROKE_WIDTH,\n    fill: 'none',\n    'stroke-linecap': 'round',\n    'stroke-linejoin': 'miter',\n    'stroke-dasharray': this._pathLength + ',' + this._pathLength\n  });\n\n  this._clip.appendChild(this._strokePath);\n  target.defs.appendChild(this._clip);\n  target.svg.appendChild(this._animationPath);\n  return this;\n};\n\nStrokeRenderer.prototype.render = function (props) {\n  if (props === this._oldProps) return;\n  if (props.displayPortion !== this._oldProps.displayPortion) {\n    this._animationPath.style.strokeDashoffset = this._getStrokeDashoffset(props.displayPortion);\n  }\n\n  var color = this._getColor(props);\n  if (color !== this._getColor(this._oldProps)) {\n    var r = color.r,\n        g = color.g,\n        b = color.b,\n        a = color.a;\n\n    svg.attrs(this._animationPath, { stroke: 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')' });\n  }\n\n  if (props.opacity !== this._oldProps.opacity) {\n    this._animationPath.style.opacity = props.opacity;\n  }\n  this._oldProps = props;\n};\n\nmodule.exports = StrokeRenderer;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar svg = __webpack_require__(3);\n\nvar _require = __webpack_require__(2),\n    getPathString = _require.getPathString;\n\nfunction UserStrokeRenderer() {\n  this._oldProps = {};\n}\n\nUserStrokeRenderer.prototype.mount = function (target) {\n  this._path = svg.createElm('path');\n  target.svg.appendChild(this._path);\n};\n\nUserStrokeRenderer.prototype.render = function (props) {\n  if (props === this._oldProps) return;\n  if (props.strokeColor !== this._oldProps.strokeColor || props.strokeWidth !== this._oldProps.strokeWidth) {\n    var _props$strokeColor = props.strokeColor,\n        r = _props$strokeColor.r,\n        g = _props$strokeColor.g,\n        b = _props$strokeColor.b,\n        a = _props$strokeColor.a;\n\n    svg.attrs(this._path, {\n      fill: 'none',\n      stroke: 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')',\n      'stroke-width': props.strokeWidth,\n      'stroke-linecap': 'round',\n      'stroke-linejoin': 'round'\n    });\n  }\n  if (props.opacity !== this._oldProps.opacity) {\n    svg.attr(this._path, 'opacity', props.opacity);\n  }\n  if (props.points !== this._oldProps.points) {\n    svg.attr(this._path, 'd', getPathString(props.points));\n  }\n  this._oldProps = props;\n};\n\nUserStrokeRenderer.prototype.destroy = function () {\n  svg.removeElm(this._path);\n};\n\nmodule.exports = UserStrokeRenderer;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar _require = __webpack_require__(3),\n    createElm = _require.createElm,\n    attrs = _require.attrs;\n\nvar RenderTargetBase = __webpack_require__(7);\n\nfunction RenderTarget(svg, defs) {\n  this.svg = svg;\n  this.defs = defs;\n  this.node = svg;\n\n  if (this.node.createSVGPoint) {\n    this._pt = this.node.createSVGPoint();\n  }\n}\nRenderTarget.prototype = Object.create(RenderTargetBase.prototype);\n\nRenderTarget.prototype.createSubRenderTarget = function () {\n  var group = createElm('g');\n  this.svg.appendChild(group);\n  return new RenderTarget(group, this.defs);\n};\n\nRenderTarget.prototype._getMousePoint = function (evt) {\n  if (this._pt) {\n    this._pt.x = evt.clientX;\n    this._pt.y = evt.clientY;\n    var localPt = this._pt.matrixTransform(this.node.getScreenCTM().inverse());\n    return { x: localPt.x, y: localPt.y };\n  }\n  return RenderTargetBase.prototype._getMousePoint.call(this, evt);\n};\n\nRenderTarget.prototype._getTouchPoint = function (evt) {\n  if (this._pt) {\n    this._pt.x = evt.touches[0].clientX;\n    this._pt.y = evt.touches[0].clientY;\n    var localPt = this._pt.matrixTransform(this.node.getScreenCTM().inverse());\n    return { x: localPt.x, y: localPt.y };\n  }\n  return RenderTargetBase.prototype._getTouchPoint.call(this, evt);\n};\n\nRenderTarget.init = function (elmOrId) {\n  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '100%';\n  var height = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '100%';\n\n  var svg = void 0;\n  var elm = elmOrId;\n  if (typeof elmOrId === 'string') {\n    elm = global.document.getElementById(elmOrId);\n  }\n  if (!elm) {\n    throw new Error('HanziWriter target element not found: ' + elmOrId);\n  }\n  var nodeType = elm.nodeName.toUpperCase();\n  if (nodeType === 'SVG' || nodeType === 'G') {\n    svg = elm;\n  } else {\n    svg = createElm('svg');\n    elm.appendChild(svg);\n  }\n  attrs(svg, { width: width, height: height });\n  var defs = createElm('defs');\n  svg.appendChild(defs);\n  return new RenderTarget(svg, defs);\n};\n\nmodule.exports = RenderTarget;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar HanziWriterRenderer = __webpack_require__(26);\nvar RenderTarget = __webpack_require__(30);\n\nmodule.exports = {\n  HanziWriterRenderer: HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init\n};\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar CharacterRenderer = __webpack_require__(27);\nvar renderUserStroke = __webpack_require__(29);\n\nvar _require = __webpack_require__(0),\n    assign = _require.assign;\n\nfunction HanziWriterRenderer(character, positioner) {\n  this._character = character;\n  this._positioner = positioner;\n  this._mainCharRenderer = new CharacterRenderer(character);\n  this._outlineCharRenderer = new CharacterRenderer(character);\n  this._highlightCharRenderer = new CharacterRenderer(character);\n}\n\nHanziWriterRenderer.prototype.mount = function (target) {\n  this._target = target;\n};\n\nHanziWriterRenderer.prototype._animationFrame = function (func) {\n  var ctx = this._target.getContext();\n  ctx.clearRect(0, 0, this._positioner.width, this._positioner.height);\n\n  ctx.save();\n  ctx.translate(this._positioner.xOffset, this._positioner.height - this._positioner.yOffset);\n  ctx.transform(1, 0, 0, -1, 0, 0);\n  ctx.scale(this._positioner.scale, this._positioner.scale);\n  func(ctx);\n  ctx.restore();\n  if (ctx.draw) ctx.draw();\n};\n\nHanziWriterRenderer.prototype.render = function (props) {\n  var _this = this;\n\n  this._animationFrame(function (ctx) {\n    _this._outlineCharRenderer.render(ctx, {\n      opacity: props.character.outline.opacity,\n      strokes: props.character.outline.strokes,\n      strokeColor: props.options.outlineColor\n    });\n    _this._mainCharRenderer.render(ctx, {\n      opacity: props.character.main.opacity,\n      strokes: props.character.main.strokes,\n      strokeColor: props.options.strokeColor,\n      radicalColor: props.options.radicalColor\n    });\n    _this._highlightCharRenderer.render(ctx, {\n      opacity: props.character.highlight.opacity,\n      strokes: props.character.highlight.strokes,\n      strokeColor: props.options.highlightColor\n    });\n\n    var userStrokes = props.userStrokes || {};\n    Object.keys(userStrokes).forEach(function (userStrokeId) {\n      if (userStrokes[userStrokeId]) {\n        var userStrokeProps = assign({\n          strokeWidth: props.options.drawingWidth,\n          strokeColor: props.options.drawingColor\n        }, userStrokes[userStrokeId]);\n        renderUserStroke(ctx, userStrokeProps);\n      }\n    });\n  });\n};\n\nHanziWriterRenderer.prototype.destroy = function () {};\n\nmodule.exports = HanziWriterRenderer;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar StrokeRenderer = __webpack_require__(28);\n\nfunction CharacterRenderer(character) {\n  this._strokeRenderers = character.strokes.map(function (stroke) {\n    return new StrokeRenderer(stroke);\n  });\n}\n\nCharacterRenderer.prototype.render = function (ctx, props) {\n  if (props.opacity < 0.05) return;\n  for (var i = 0; i < this._strokeRenderers.length; i++) {\n    this._strokeRenderers[i].render(ctx, {\n      strokeColor: props.strokeColor,\n      radicalColor: props.radicalColor,\n      opacity: props.strokes[i].opacity * props.opacity,\n      displayPortion: props.strokes[i].displayPortion\n    });\n  }\n};\n\nmodule.exports = CharacterRenderer;\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar _require = __webpack_require__(2),\n    extendStart = _require.extendStart;\n\nvar _require2 = __webpack_require__(8),\n    drawPath = _require2.drawPath,\n    pathStringToCanvas = _require2.pathStringToCanvas;\n\nvar StrokeRendererBase = __webpack_require__(6);\n\nvar STROKE_WIDTH = 200;\n\n// this is a stroke composed of several stroke parts\nfunction StrokeRenderer(stroke) {\n  var usePath2D = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n  this._stroke = stroke;\n  this._pathLength = stroke.getLength() + STROKE_WIDTH / 2;\n  if (usePath2D && global.Path2D) {\n    this._path2D = new global.Path2D(this._stroke.path);\n  } else {\n    this._pathCmd = pathStringToCanvas(this._stroke.path);\n  }\n  this._extendedMaskPoints = extendStart(this._stroke.points, STROKE_WIDTH / 2);\n}\nStrokeRenderer.prototype = Object.create(StrokeRendererBase.prototype);\n\nStrokeRenderer.prototype.render = function (ctx, props) {\n  if (props.opacity < 0.05) return;\n\n  ctx.save();\n  if (this._path2D) {\n    ctx.clip(this._path2D);\n  } else {\n    this._pathCmd(ctx);\n    // wechat bugs out if the clip path isn't stroked or filled\n    ctx.globalAlpha = 0;\n    ctx.stroke();\n    ctx.clip();\n  }\n\n  var _getColor = this._getColor(props),\n      r = _getColor.r,\n      g = _getColor.g,\n      b = _getColor.b,\n      a = _getColor.a;\n\n  var color = a === 1 ? 'rgb(' + r + ',' + g + ',' + b + ')' : 'rgb(' + r + ',' + g + ',' + b + ',' + a + ')';\n  var dashOffset = this._getStrokeDashoffset(props.displayPortion);\n  ctx.globalAlpha = props.opacity;\n  ctx.strokeStyle = color;\n  ctx.fillStyle = color;\n  ctx.lineWidth = STROKE_WIDTH;\n  ctx.lineCap = 'round';\n  ctx.lineJoin = 'round';\n  // wechat sets dashOffset as a second param here. Should be harmless for browsers to add here too\n  ctx.setLineDash([this._pathLength, this._pathLength], dashOffset);\n  ctx.lineDashOffset = dashOffset;\n  drawPath(ctx, this._extendedMaskPoints);\n\n  ctx.restore();\n};\n\nmodule.exports = StrokeRenderer;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(8),\n    drawPath = _require.drawPath;\n\nmodule.exports = function (ctx, props) {\n  if (props.opacity < 0.05) return;\n  var _props$strokeColor = props.strokeColor,\n      r = _props$strokeColor.r,\n      g = _props$strokeColor.g,\n      b = _props$strokeColor.b,\n      a = _props$strokeColor.a;\n\n\n  ctx.save();\n  ctx.globalAlpha = props.opacity;\n  ctx.lineWidth = props.strokeWidth;\n  ctx.strokeStyle = 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')';\n  ctx.lineCap = 'round';\n  ctx.lineJoin = 'round';\n  drawPath(ctx, props.points);\n  ctx.restore();\n};\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar RenderTargetBase = __webpack_require__(7);\n\nfunction RenderTarget(canvas) {\n  this.node = canvas;\n}\nRenderTarget.prototype = Object.create(RenderTargetBase.prototype);\n\nRenderTarget.prototype.getContext = function () {\n  return this.node.getContext('2d');\n};\n\nRenderTarget.init = function (elmOrId) {\n  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '100%';\n  var height = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '100%';\n\n  var canvas = void 0;\n  var elm = elmOrId;\n  if (typeof elmOrId === 'string') {\n    elm = global.document.getElementById(elmOrId);\n  }\n  if (!elm) {\n    throw new Error('HanziWriter target element not found: ' + elmOrId);\n  }\n  var nodeType = elm.nodeName.toUpperCase();\n  if (nodeType === 'CANVAS') {\n    canvas = elm;\n  } else {\n    canvas = global.document.createElement('canvas');\n    elm.appendChild(canvas);\n  }\n  canvas.setAttribute('width', width);\n  canvas.setAttribute('height', height);\n  return new RenderTarget(canvas);\n};\n\nmodule.exports = RenderTarget;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar VERSION = '2.0';\nvar getCharDataUrl = function getCharDataUrl(char) {\n  return 'https://cdn.jsdelivr.net/npm/hanzi-writer-data@' + VERSION + '/' + char + '.json';\n};\n\nmodule.exports = function (char, onLoad, onError) {\n  // load char data from hanziwriter cdn (currently hosted on jsdelivr)\n  var xhr = new global.XMLHttpRequest();\n  if (xhr.overrideMimeType) {\n    // IE 9 and 10 don't seem to support this...\n    xhr.overrideMimeType('application/json');\n  }\n  xhr.open('GET', getCharDataUrl(char), true);\n  xhr.onerror = function (event) {\n    onError(xhr, event);\n  };\n  xhr.onreadystatechange = function () {\n    // TODO: error handling\n    if (xhr.readyState !== 4) return;\n\n    if (xhr.status === 200) {\n      onLoad(JSON.parse(xhr.responseText));\n    } else if (xhr.status !== 0 && onError) {\n      onError(xhr);\n    }\n  };\n  xhr.send(null);\n};\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n\nvar _require = __webpack_require__(0),\n    callIfExists = _require.callIfExists;\n\nfunction LoadingManager(options) {\n  this._loadCounter = 0;\n  this._options = options;\n  this._isLoading = false;\n\n  // use this to attribute to determine if there was a problem with loading\n  this.loadingFailed = false;\n}\n\nLoadingManager.prototype._debouncedLoad = function (char, count) {\n  var _this = this;\n\n  // these wrappers ignore all responses except the most recent.\n  var wrappedResolve = function wrappedResolve(data) {\n    if (count === _this._loadCounter) _this._resolve(data);\n  };\n  var wrappedReject = function wrappedReject(reason) {\n    if (count === _this._loadCounter) _this._reject(reason);\n  };\n\n  var returnedData = this._options.charDataLoader(char, wrappedResolve, wrappedReject);\n  if (returnedData) wrappedResolve(returnedData);\n};\n\nLoadingManager.prototype._setupLoadingPromise = function () {\n  var _this2 = this;\n\n  return new Promise(function (resolve, reject) {\n    _this2._resolve = resolve;\n    _this2._reject = reject;\n  }).then(function (data) {\n    _this2._isLoading = false;\n    callIfExists(_this2._options.onLoadCharDataSuccess, data);\n    return data;\n  }, function (reason) {\n    _this2._isLoading = false;\n    _this2.loadingFailed = true;\n    callIfExists(_this2._options.onLoadCharDataError, reason);\n    // If error callback wasn't provided, throw an error so the developer will be aware something went wrong\n    if (!_this2._options.onLoadCharDataError) {\n      if (reason instanceof Error) throw reason;\n      var err = new Error('Failed to load char data for ' + _this2._loadingChar);\n      err.reason = reason;\n      throw err;\n    }\n  });\n};\n\nLoadingManager.prototype.loadCharData = function (char) {\n  this._loadingChar = char;\n  var promise = this._setupLoadingPromise();\n  this.loadingFailed = false;\n  this._isLoading = true;\n  this._loadCounter++;\n  this._debouncedLoad(char, this._loadCounter);\n  return promise;\n};\n\nmodule.exports = LoadingManager;\n\n/***/ })\n/******/ ]);\n});"]}
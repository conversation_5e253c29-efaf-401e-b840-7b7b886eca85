# Canvas弹窗隐藏功能说明

## 功能概述
为了解决微信小程序Canvas原生组件层级过高，会遮挡弹窗的问题，我们实现了在弹窗显示时隐藏Canvas，弹窗关闭时重新显示Canvas的功能。

## 实现方案

### 1. 条件显示Canvas
使用`wx:if`指令控制Canvas的显示和隐藏：

```xml
<canvas 
  class="writing-canvas" 
  canvas-id="writingCanvas"
  bindtouchstart="onTouchStart"
  bindtouchmove="onTouchMove"
  bindtouchend="onTouchEnd"
  disable-scroll="true"
  wx:if="{{!showSettings && !showResult && !isLoading}}"
></canvas>
```

### 2. 智能占位区域
当Canvas隐藏时，显示一个同样尺寸的占位区域，保持界面布局稳定：

```xml
<!-- 弹窗显示时的占位区域 -->
<view class="canvas-placeholder" wx:if="{{showSettings || showResult || isLoading}}">
  <view class="placeholder-content">
    <text class="placeholder-icon" wx:if="{{showSettings}}">⚙️</text>
    <text class="placeholder-icon" wx:elif="{{showResult}}">📝</text>
    <text class="placeholder-icon" wx:elif="{{isLoading}}">⏳</text>
    <text class="placeholder-text" wx:if="{{showSettings}}">设置面板已打开</text>
    <text class="placeholder-text" wx:elif="{{showResult}}">答题反馈显示中</text>
    <text class="placeholder-text" wx:elif="{{isLoading}}">{{loadingText}}</text>
  </view>
</view>
```

## 支持的弹窗状态

### 1. 设置弹窗 (`showSettings`)
- **触发时机**：点击浮动设置按钮
- **关闭时机**：点击关闭按钮或完成设置
- **占位显示**：⚙️ "设置面板已打开"

### 2. 答题结果反馈 (`showResult`)
- **触发时机**：提交答案后显示正确/错误反馈
- **关闭时机**：点击"继续"按钮
- **占位显示**：📝 "答题反馈显示中"

### 3. 加载状态 (`isLoading`)
- **触发时机**：点击识别按钮，开始字迹识别
- **关闭时机**：识别完成（约1.5秒）
- **占位显示**：⏳ 动态显示loadingText内容

## 技术优势

### 1. 完美解决层级问题
- Canvas原生组件层级固定，无法通过z-index调整
- 条件隐藏Canvas彻底解决了遮挡弹窗的问题

### 2. 用户体验优化
- 占位区域保持界面布局稳定，避免页面跳动
- 不同状态显示不同图标和文字，提供清晰的状态反馈
- Canvas隐藏和显示过程流畅，用户感知良好

### 3. 数据保护
- Canvas隐藏时，手写数据（strokes）完全保留
- 重新显示Canvas时，通过redrawCanvas()自动恢复所有笔画
- 用户的书写内容不会丢失

### 4. 兼容性保障
- 使用微信小程序原生的条件渲染机制
- 不依赖复杂的层级管理，兼容性极佳
- 支持所有微信小程序版本

## 代码结构

### WXML结构
```
手写区域
├── Canvas (条件显示)
├── 占位区域 (条件显示) 
│   ├── 设置状态
│   ├── 结果反馈状态
│   └── 加载状态
└── 书写提示
```

### 控制变量
- `showSettings`: 设置弹窗显示状态
- `showResult`: 答题结果反馈显示状态  
- `isLoading`: 加载状态
- `loadingText`: 动态加载文字

### CSS样式
占位区域完全复制Canvas的样式：
```css
.canvas-placeholder {
  width: 100%;
  height: 400rpx;
  border: 4rpx dashed var(--border-color);
  border-radius: var(--border-radius-md);
  background: #FAFBFC;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

## 使用场景

1. **练习设置**：用户打开设置面板调整练习参数
2. **答题反馈**：显示答题正确性和得分情况
3. **字迹识别**：系统正在处理手写字迹识别
4. **任何新增弹窗**：扩展性强，支持新增弹窗状态

## 扩展方法

如需添加新的弹窗状态，只需：

1. 在data中添加新的布尔变量（如`showNewModal`）
2. 修改Canvas的wx:if条件：`wx:if="{{!showSettings && !showResult && !isLoading && !showNewModal}}"`
3. 修改占位区域的wx:if条件：`wx:if="{{showSettings || showResult || isLoading || showNewModal}}"`
4. 在占位区域内容中添加新状态的图标和文字显示

这种设计模式具有很强的扩展性和维护性，可以轻松适应未来的功能需求。 
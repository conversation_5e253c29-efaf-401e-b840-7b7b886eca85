# 听写挑战功能设计

## 1. 功能描述

听写挑战是小学字词听写软件的核心功能，通过语音播放和手写识别技术，为学生提供真实的听写练习体验。

### 1.1 核心功能
- **语音播放**：支持汉字、词语的标准普通话朗读
- **手写识别**：实时识别学生手写内容，智能判断正确性
- **练习模式**：全选、类型选、单选三种练习方式
- **智能评分**：基于笔画、结构、字形的综合评分
- **实时反馈**：即时显示书写结果和改进建议

### 1.2 用户价值
- 模拟真实听写场景，提高学习效果
- 智能纠错，帮助学生发现书写问题
- 个性化练习，满足不同学习需求
- 减轻家长负担，提供专业指导

## 2. 实现流程

### 2.1 听写开始流程
```
选择练习内容 → 设置练习参数 → 进入听写界面 → 语音播放 → 学生手写 → 智能识别 → 结果反馈 → 下一题/结束
```

### 2.2 手写识别流程
```
学生开始书写 → 实时捕获笔迹 → 笔迹预处理 → AI识别引擎 → 字符匹配 → 置信度计算 → 结果输出 → 用户确认
```

### 2.3 评分计算流程
```
识别结果 → 字形匹配度 → 笔画顺序检查 → 结构合理性 → 字体规范性 → 综合评分 → 等级判定 → 建议生成
```

## 3. 业务规则

### 3.1 练习模式规则
- **全选模式**：选择所有字词进行随机练习
- **类型选模式**：按写字表/识字表/词汇表分类练习
- **单选模式**：自定义选择特定字词练习

### 3.2 识别精度规则
- **准确率要求**：单字识别准确率 ≥ 95%
- **响应时间**：识别响应时间 ≤ 1秒
- **容错机制**：支持字形相似度匹配
- **笔画顺序**：提供笔画顺序提示和检查

### 3.3 评分标准
- **A级（90-100分）**：字形标准，笔画正确
- **B级（80-89分）**：字形基本正确，有小瑕疵
- **C级（70-79分）**：字形识别正确，需改进
- **D级（60-69分）**：勉强识别，需重写
- **E级（<60分）**：无法识别或完全错误

## 4. 界面设计要求

### 4.1 练习设置页面
```
┌─────────────────────────────────────────┐
│ ←  听写挑战                            │
├─────────────────────────────────────────┤
│                                        │
│  📖 已选择内容                          │
│  ┌─────────────────────────────────────┐ │
│  │ 人教版 - 三年级上册 - 写字表         │ │
│  │ 第一单元：共 32 个字词              │ │
│  │                             ✏️ 更换 │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ⚙️ 练习设置                            │
│                                        │
│  🎯 练习模式                            │
│  ● 全选练习 (32个字词)                  │
│  ○ 随机抽取 ┌───┐ 个字词               │
│            │ 10│                    │
│            └───┘                    │
│  ○ 错题重练 (5个字词)                   │
│                                        │
│  🔊 语音设置                            │
│  ┌─────────────────────────────────────┐ │
│  │ 播放速度    ● 正常  ○ 慢速  ○ 快速  │ │
│  │ 重复次数    ● 2次   ○ 3次   ○ 1次  │ │
│  │ 间隔时间    ● 3秒   ○ 5秒   ○ 2秒  │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ✏️ 书写设置                            │
│  ┌─────────────────────────────────────┐ │
│  │ ☑️ 显示田字格                        │ │
│  │ ☑️ 笔画提示                          │ │
│  │ ☑️ 实时识别                          │ │
│  │ ☑️ 自动保存                          │ │
│  └─────────────────────────────────────┘ │
│                                        │
│                              ┌───────┐ │
│                              │ 开始练习│ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.2 听写练习主界面
```
┌─────────────────────────────────────────┐
│ ←  听写练习              ⏸️ 暂停  ⚙️ 设置 │
├─────────────────────────────────────────┤
│                                        │
│  📊 进度：8/32           ⏱️ 05:23       │
│  ████████░░░░░░░░░░░░ 25%               │
│                                        │
│  🔊 正在播放                            │
│  ┌─────────────────────────────────────┐ │
│  │                苹果                 │ │
│  │          [píng guǒ]                │ │
│  │                                    │ │
│  │     🔊 ▶️ 播放  🔄 重播  ⏭️ 跳过    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ✏️ 请在下方田字格中书写                │
│  ┌─────────────────────────────────────┐ │
│  │ ┌─────────┐ ┌─────────┐             │ │
│  │ │   苹    │ │         │             │ │
│  │ │ ┼   ┼   │ │ ┼   ┼   │             │ │
│  │ │ ─   ─   │ │ ─   ─   │             │ │
│  │ │ ┼   ┼   │ │ ┼   ┼   │             │ │
│  │ └─────────┘ └─────────┘             │ │
│  │                                    │ │
│  │  🎯 识别结果：苹 (95% 置信度)        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │   ✏️    │ │   🗑️    │ │   ✅    │   │
│  │   重写   │ │   清除   │ │   确认   │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  💡 提示：注意"苹"字右边"平"的写法       │
└─────────────────────────────────────────┘
```

### 4.3 答题结果页面
```
┌─────────────────────────────────────────┐
│ ←  答题结果                            │
├─────────────────────────────────────────┤
│                                        │
│  🎉 完成听写！                          │
│                                        │
│  📊 本次成绩                            │
│  ┌─────────────────────────────────────┐ │
│  │           综合得分                  │ │
│  │             85分                   │ │
│  │            ⭐⭐⭐⭐                 │ │
│  │                                    │ │
│  │  正确率  26/32  81.3%  ████████░░  │ │
│  │  准确度  平均 88分     ████████░░  │ │
│  │  用时    08:45        ██████░░░░  │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📈 详细分析                            │
│  ┌─────────────────────────────────────┐ │
│  │  ✅ 正确 (26个)                     │ │
│  │  苹果、困难、温暖、明亮...           │ │
│  │                                    │ │
│  │  ❌ 错误 (6个)                      │ │
│  │  📝 影 → 景  (字形相似)            │ │
│  │  📝 暖 → 爱  (笔画错误)            │ │
│  │  📝 粗 → 组  (结构问题)            │ │
│  │  📝 练 → 炼  (偏旁错误)            │ │
│  │      查看详情 >                    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🎁 获得奖励                            │
│  ┌─────────────────────────────────────┐ │
│  │  🎖️ +50 积分                        │ │
│  │  🏆 获得成就：坚持不懈               │ │
│  │  📊 等级提升：小学者 → 小能手        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ 📝 错题本 │ │ 🔄 再来 │ │ 🏠 回首页│   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
```

### 4.4 笔画提示页面
```
┌─────────────────────────────────────────┐
│ ←  笔画提示                    ❌ 关闭   │
├─────────────────────────────────────────┤
│                                        │
│  ✏️ "苹" 字笔画顺序                     │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │              笔画演示               │ │
│  │                                    │ │
│  │         ┌─────────┐                │ │
│  │         │         │                │ │
│  │         │    苹   │                │ │
│  │         │         │                │ │
│  │         └─────────┘                │ │
│  │                                    │ │
│  │          ▶️ 播放动画                │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📝 笔画分解 (共11画)                   │
│  ┌─────────────────────────────────────┐ │
│  │  1. 一 (横)     ● 已完成           │ │
│  │  2. 丨 (竖)     ● 已完成           │ │
│  │  3. 丨 (竖)     ● 已完成           │ │
│  │  4. ㇕ (撇折)   ● 已完成           │ │
│  │  5. 一 (横)     ○ 当前笔画         │ │
│  │  6. 一 (横)     ○ 未写             │ │
│  │  7. 丨 (竖)     ○ 未写             │ │
│  │  8. ㇏ (捺)     ○ 未写             │ │
│  │  9. 丶 (点)     ○ 未写             │ │
│  │  10. 一 (横)    ○ 未写             │ │
│  │  11. 丨 (竖)    ○ 未写             │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  💡 书写要点                            │
│  ┌─────────────────────────────────────┐ │
│  │  • 左边"艹"字头要写得扁一些          │ │
│  │  • 右边"平"字注意最后一笔是竖        │ │
│  │  • 整体结构要左右平衡               │ │
│  └─────────────────────────────────────┘ │
│                                        │
│                              ┌───────┐ │
│                              │ 开始书写│ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.5 暂停菜单
```
┌─────────────────────────────────────────┐
│                                        │
│              练习已暂停                 │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │                                    │ │
│  │           ⏸️ 暂停中                │ │
│  │                                    │ │
│  │     当前进度：8/32 (25%)            │ │
│  │     用时：05:23                    │ │
│  │                                    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │           ▶️ 继续练习                │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │           ⚙️ 练习设置                │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │           💾 保存退出                │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │           🏠 放弃练习                │ │
│  └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

### 4.6 设计风格要求

1. **色彩运用**
   - 主色调：清新蓝色(#4A90E2)表示学习状态
   - 成功色：绿色(#52C41A)表示正确答案
   - 警告色：橙色(#FA8C16)表示需要注意
   - 错误色：红色(#F5222D)表示错误答案

2. **交互设计**
   - 大按钮设计，适合儿童操作
   - 清晰的状态反馈
   - 友好的错误提示
   - 及时的操作确认

3. **视觉层次**
   - 重要信息突出显示
   - 次要信息灰色处理
   - 分组清晰，层次分明

## 5. 技术实现

### 5.1 前端实现（微信小程序）

#### 听写练习页面
```javascript
// pages/dictation-practice/index.js
Page({
  data: {
    currentIndex: 0,
    totalCount: 0,
    wordList: [],
    currentWord: null,
    isPlaying: false,
    isPaused: false,
    startTime: null,
    elapsedTime: 0,
    score: 0,
    correctCount: 0,
    answers: [],
    handwritingResult: '',
    confidence: 0,
    settings: {
      playSpeed: 'normal',
      repeatTimes: 2,
      interval: 3,
      showGrid: true,
      showStroke: true,
      autoRecognition: true
    }
  },

  onLoad(options) {
    const { contentId, mode } = options;
    this.loadWordList(contentId, mode);
    this.initHandwriting();
  },

  onUnload() {
    this.saveProgress();
  },

  // 加载字词列表
  async loadWordList(contentId, mode) {
    try {
      wx.showLoading({ title: '准备中...' });
      
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/dictation/words`,
        method: 'GET',
        data: { contentId, mode }
      });

      const wordList = res.data;
      this.setData({
        wordList,
        totalCount: wordList.length,
        currentWord: wordList[0],
        startTime: Date.now()
      });

      this.startDictation();
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 开始听写
  startDictation() {
    this.playCurrentWord();
  },

  // 播放当前字词
  async playCurrentWord() {
    const { currentWord, settings } = this.data;
    
    this.setData({ isPlaying: true });

    try {
      // 播放语音
      await this.playAudio(currentWord.audio_url, settings.playSpeed);
      
      // 重复播放
      for (let i = 1; i < settings.repeatTimes; i++) {
        await this.sleep(settings.interval * 1000);
        await this.playAudio(currentWord.audio_url, settings.playSpeed);
      }
    } catch (error) {
      wx.showToast({ title: '播放失败', icon: 'error' });
    }

    this.setData({ isPlaying: false });
  },

  // 播放音频
  playAudio(url, speed = 'normal') {
    return new Promise((resolve, reject) => {
      const audioContext = wx.createInnerAudioContext();
      
      audioContext.src = url;
      audioContext.playbackRate = this.getPlaybackRate(speed);
      
      audioContext.onPlay(() => {
        console.log('开始播放');
      });
      
      audioContext.onEnded(() => {
        audioContext.destroy();
        resolve();
      });
      
      audioContext.onError((error) => {
        audioContext.destroy();
        reject(error);
      });
      
      audioContext.play();
    });
  },

  // 获取播放速率
  getPlaybackRate(speed) {
    const rates = {
      'slow': 0.8,
      'normal': 1.0,
      'fast': 1.2
    };
    return rates[speed] || 1.0;
  },

  // 初始化手写识别
  initHandwriting() {
    // 初始化手写板组件
    this.handwritingCanvas = this.selectComponent('#handwriting-canvas');
    
    // 绑定手写事件
    this.handwritingCanvas.onStrokeEnd = (strokes) => {
      if (this.data.settings.autoRecognition) {
        this.recognizeHandwriting(strokes);
      }
    };
  },

  // 手写识别
  async recognizeHandwriting(strokes) {
    try {
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/handwriting/recognize`,
        method: 'POST',
        data: {
          strokes: strokes,
          target: this.data.currentWord.word
        }
      });

      const { result, confidence } = res.data;
      
      this.setData({
        handwritingResult: result,
        confidence: confidence
      });

      // 自动确认高置信度结果
      if (confidence > 0.9) {
        setTimeout(() => {
          this.confirmAnswer();
        }, 1000);
      }
    } catch (error) {
      console.error('识别失败:', error);
    }
  },

  // 确认答案
  confirmAnswer() {
    const { currentWord, handwritingResult, confidence } = this.data;
    const isCorrect = handwritingResult === currentWord.word;
    
    // 计算得分
    const score = this.calculateScore(isCorrect, confidence);
    
    // 记录答案
    const answer = {
      word: currentWord.word,
      userAnswer: handwritingResult,
      isCorrect: isCorrect,
      confidence: confidence,
      score: score,
      timestamp: Date.now()
    };

    this.data.answers.push(answer);
    
    if (isCorrect) {
      this.setData({ correctCount: this.data.correctCount + 1 });
    }

    // 显示结果反馈
    this.showAnswerFeedback(answer);
    
    // 准备下一题
    setTimeout(() => {
      this.nextWord();
    }, 2000);
  },

  // 计算得分
  calculateScore(isCorrect, confidence) {
    if (!isCorrect) return 0;
    
    // 基础分数
    let score = 60;
    
    // 置信度加分
    score += confidence * 40;
    
    return Math.round(score);
  },

  // 显示答案反馈
  showAnswerFeedback(answer) {
    const icon = answer.isCorrect ? 'success' : 'error';
    const title = answer.isCorrect ? 
      `正确！得分：${answer.score}` : 
      `错误！正确答案：${answer.word}`;
    
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  },

  // 下一题
  nextWord() {
    const { currentIndex, totalCount } = this.data;
    
    if (currentIndex + 1 < totalCount) {
      // 还有下一题
      const nextIndex = currentIndex + 1;
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.wordList[nextIndex],
        handwritingResult: '',
        confidence: 0
      });
      
      // 清空手写板
      this.handwritingCanvas.clear();
      
      // 播放下一题
      this.playCurrentWord();
    } else {
      // 完成所有题目
      this.finishDictation();
    }
  },

  // 完成听写
  finishDictation() {
    const endTime = Date.now();
    const totalTime = endTime - this.data.startTime;
    
    // 计算总成绩
    const totalScore = this.calculateTotalScore();
    
    // 保存练习记录
    this.savePracticeRecord(totalScore, totalTime);
    
    // 跳转到结果页面
    wx.redirectTo({
      url: `/pages/dictation-result/index?score=${totalScore}&time=${totalTime}&correct=${this.data.correctCount}&total=${this.data.totalCount}`
    });
  },

  // 计算总成绩
  calculateTotalScore() {
    const { answers, totalCount } = this.data;
    const totalScore = answers.reduce((sum, answer) => sum + answer.score, 0);
    return Math.round(totalScore / totalCount);
  },

  // 保存练习记录
  async savePracticeRecord(score, time) {
    try {
      await wx.request({
        url: `${app.globalData.apiUrl}/practice/save`,
        method: 'POST',
        data: {
          answers: this.data.answers,
          score: score,
          time: time,
          correctCount: this.data.correctCount,
          totalCount: this.data.totalCount
        },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });
    } catch (error) {
      console.error('保存记录失败:', error);
    }
  },

  // 重播按钮
  onReplay() {
    this.playCurrentWord();
  },

  // 跳过按钮
  onSkip() {
    // 记录跳过的答案
    const answer = {
      word: this.data.currentWord.word,
      userAnswer: '',
      isCorrect: false,
      confidence: 0,
      score: 0,
      skipped: true,
      timestamp: Date.now()
    };
    
    this.data.answers.push(answer);
    this.nextWord();
  },

  // 清除手写
  onClear() {
    this.handwritingCanvas.clear();
    this.setData({
      handwritingResult: '',
      confidence: 0
    });
  },

  // 暂停练习
  onPause() {
    this.setData({ isPaused: true });
    this.saveProgress();
  },

  // 继续练习
  onResume() {
    this.setData({ isPaused: false });
  },

  // 保存进度
  saveProgress() {
    const progress = {
      currentIndex: this.data.currentIndex,
      answers: this.data.answers,
      startTime: this.data.startTime,
      wordList: this.data.wordList
    };
    
    wx.setStorageSync('dictation_progress', progress);
  },

  // 辅助方法
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
});
```

#### 手写识别组件
```javascript
// components/handwriting-canvas/index.js
Component({
  properties: {
    width: {
      type: Number,
      value: 300
    },
    height: {
      type: Number,
      value: 300
    },
    showGrid: {
      type: Boolean,
      value: true
    }
  },

  data: {
    isDrawing: false,
    currentStroke: [],
    allStrokes: []
  },

  lifetimes: {
    attached() {
      this.initCanvas();
    }
  },

  methods: {
    // 初始化画布
    initCanvas() {
      const query = this.createSelectorQuery();
      query.select('#canvas').fields({ node: true, size: true }).exec((res) => {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;
        ctx.scale(dpr, dpr);
        
        this.canvas = canvas;
        this.ctx = ctx;
        
        this.setupCanvas();
        this.drawGrid();
      });
    },

    // 设置画布
    setupCanvas() {
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.lineWidth = 3;
      this.ctx.strokeStyle = '#333';
    },

    // 绘制田字格
    drawGrid() {
      if (!this.properties.showGrid) return;
      
      const { width, height } = this.properties;
      const ctx = this.ctx;
      
      ctx.save();
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 1;
      
      // 绘制外框
      ctx.strokeRect(0, 0, width, height);
      
      // 绘制十字线
      ctx.beginPath();
      ctx.moveTo(width / 2, 0);
      ctx.lineTo(width / 2, height);
      ctx.moveTo(0, height / 2);
      ctx.lineTo(width, height / 2);
      ctx.stroke();
      
      ctx.restore();
    },

    // 触摸开始
    onTouchStart(e) {
      const touch = e.touches[0];
      const rect = e.currentTarget.getBoundingClientRect();
      
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;
      
      this.setData({ isDrawing: true });
      this.data.currentStroke = [{ x, y, timestamp: Date.now() }];
      
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
    },

    // 触摸移动
    onTouchMove(e) {
      if (!this.data.isDrawing) return;
      
      const touch = e.touches[0];
      const rect = e.currentTarget.getBoundingClientRect();
      
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;
      
      this.data.currentStroke.push({ x, y, timestamp: Date.now() });
      
      this.ctx.lineTo(x, y);
      this.ctx.stroke();
    },

    // 触摸结束
    onTouchEnd(e) {
      if (!this.data.isDrawing) return;
      
      this.setData({ isDrawing: false });
      this.data.allStrokes.push([...this.data.currentStroke]);
      
      // 触发笔画结束事件
      if (this.onStrokeEnd) {
        this.onStrokeEnd(this.data.allStrokes);
      }
    },

    // 清除画布
    clear() {
      this.ctx.clearRect(0, 0, this.properties.width, this.properties.height);
      this.drawGrid();
      this.data.allStrokes = [];
      this.data.currentStroke = [];
    },

    // 撤销最后一笔
    undo() {
      if (this.data.allStrokes.length === 0) return;
      
      this.data.allStrokes.pop();
      this.redraw();
    },

    // 重新绘制
    redraw() {
      this.ctx.clearRect(0, 0, this.properties.width, this.properties.height);
      this.drawGrid();
      
      this.data.allStrokes.forEach(stroke => {
        if (stroke.length === 0) return;
        
        this.ctx.beginPath();
        this.ctx.moveTo(stroke[0].x, stroke[0].y);
        
        for (let i = 1; i < stroke.length; i++) {
          this.ctx.lineTo(stroke[i].x, stroke[i].y);
        }
        
        this.ctx.stroke();
      });
    }
  }
});
```

### 5.2 后端实现（Node.js）

#### 手写识别API
```javascript
// routes/handwriting.js
const express = require('express');
const router = express.Router();
const HandwritingService = require('../services/handwriting');

// 手写识别
router.post('/recognize', async (req, res) => {
  try {
    const { strokes, target } = req.body;
    
    // 调用识别服务
    const result = await HandwritingService.recognize(strokes, target);
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取笔画顺序
router.get('/strokes/:character', async (req, res) => {
  try {
    const { character } = req.params;
    
    const strokes = await HandwritingService.getStrokeOrder(character);
    
    res.json(strokes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

#### 听写练习API
```javascript
// routes/dictation.js
const express = require('express');
const router = express.Router();
const DictationService = require('../services/dictation');
const auth = require('../middleware/auth');

// 获取练习字词
router.get('/words', async (req, res) => {
  try {
    const { contentId, mode } = req.query;
    
    const words = await DictationService.getPracticeWords(contentId, mode);
    
    res.json(words);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 保存练习记录
router.post('/save', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const practiceData = req.body;
    
    const record = await DictationService.savePracticeRecord(userId, practiceData);
    
    res.json(record);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

## 6. 数据接口

### 6.1 手写识别接口

#### 识别手写字符
- **接口**: `POST /api/handwriting/recognize`
- **参数**:
```json
{
  "strokes": [
    [
      {"x": 10, "y": 20, "timestamp": 1642678901234},
      {"x": 15, "y": 25, "timestamp": 1642678901240}
    ]
  ],
  "target": "苹"
}
```
- **返回**:
```json
{
  "result": "苹",
  "confidence": 0.95,
  "candidates": [
    {"character": "苹", "confidence": 0.95},
    {"character": "萍", "confidence": 0.85}
  ]
}
```

### 6.2 听写练习接口

#### 获取练习字词
- **接口**: `GET /api/dictation/words`
- **参数**:
  - `contentId`: 内容ID
  - `mode`: 练习模式 (all/random/error)
- **返回**:
```json
{
  "data": [
    {
      "id": 1,
      "word": "苹果",
      "pinyin": "píng guǒ",
      "definition": "一种水果",
      "audio_url": "https://example.com/audio/pingguo.mp3"
    }
  ]
}
```

## 7. 测试用例

### 7.1 功能测试

#### 手写识别测试
1. **准确率测试**
   - 单字识别准确率验证
   - 词语识别准确率验证
   - 草书、楷书等字体适应性

2. **响应时间测试**
   - 识别响应时间 < 1秒
   - 大量笔画的处理能力
   - 网络延迟影响测试

#### 语音播放测试
1. **播放质量测试**
   - 音质清晰度
   - 语速适中性
   - 发音准确性

2. **播放控制测试**
   - 重播功能
   - 速度调节
   - 音量控制

### 7.2 性能测试

#### 识别性能
- 单次识别耗时 < 1秒
- 连续识别稳定性
- 内存使用合理性

#### 渲染性能
- 手写画布流畅度
- 界面响应速度
- 动画效果流畅性 
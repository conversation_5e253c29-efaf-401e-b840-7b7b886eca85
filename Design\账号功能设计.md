# 账号功能设计

## 1. 功能描述

### 1.1 功能概述
账号功能是小学字词听写软件的基础功能模块，负责用户身份认证、个人信息管理和称号系统。通过微信小程序授权登录，为用户提供个性化的学习体验和成就展示。

### 1.2 核心功能
- **微信授权登录**：快速登录，获取用户基础信息
- **用户信息管理**：头像、昵称、学习档案管理
- **称号系统**：基于积分和成就的等级称号
- **个人中心**：统一的用户信息展示和设置入口

### 1.3 功能价值
- 降低登录门槛，提升用户体验
- 建立用户身份体系，支持个性化功能
- 激励用户学习，增强产品粘性
- 为家长和老师提供学习数据追踪

## 2. 实现流程

### 2.1 微信登录流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant MP as 小程序
    participant WX as 微信服务器
    participant API as 后端API
    participant DB as 数据库

    User->>MP: 点击登录按钮
    MP->>WX: wx.login()获取code
    WX-->>MP: 返回临时code
    MP->>API: 发送code到后端
    API->>WX: code换取session_key和openid
    WX-->>API: 返回session_key和openid
    API->>DB: 查询/创建用户记录
    DB-->>API: 返回用户信息
    API-->>MP: 返回自定义登录态
    MP->>MP: 保存登录状态
    MP-->>User: 登录成功，进入主页
```

### 2.2 用户信息更新流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant MP as 小程序
    participant API as 后端API
    participant DB as 数据库

    User->>MP: 点击授权获取用户信息
    MP->>User: 显示授权确认弹窗
    User->>MP: 确认授权
    MP->>MP: wx.getUserProfile()
    MP->>API: 发送用户信息到后端
    API->>DB: 更新用户信息
    DB-->>API: 返回更新结果
    API-->>MP: 返回成功状态
    MP-->>User: 显示信息更新成功
```

### 2.3 称号升级流程
```mermaid
sequenceDiagram
    participant System as 系统
    participant API as 后端API
    participant DB as 数据库
    participant MP as 小程序
    participant User as 用户

    System->>API: 练习完成，积分变化
    API->>DB: 更新用户积分
    API->>API: 检查称号升级条件
    API->>DB: 查询称号规则
    DB-->>API: 返回称号数据
    API->>API: 计算新称号
    API->>DB: 更新用户称号
    API->>MP: 推送称号升级消息
    MP-->>User: 显示升级动画和奖励
```

## 3. 业务规则

### 3.1 登录规则
- **登录方式**：仅支持微信小程序授权登录
- **用户标识**：使用openid作为唯一标识
- **登录有效期**：30天免登录，过期后需重新授权
- **首次登录**：自动创建用户档案，分配默认称号

### 3.2 用户信息规则
```javascript
// 用户信息验证规则
const userInfoRules = {
  nickname: {
    maxLength: 20,
    required: true,
    filter: ['敏感词过滤', '特殊字符过滤']
  },
  grade: {
    range: [1, 6],
    required: false,
    default: null
  },
  school: {
    maxLength: 50,
    required: false
  },
  avatar: {
    format: ['jpg', 'png', 'gif'],
    maxSize: '2MB',
    required: false
  }
}
```

### 3.3 称号系统规则
```javascript
// 称号等级配置
const titleLevels = [
  { level: 1, title: '小学徒', minPoints: 0, maxPoints: 99 },
  { level: 2, title: '初学者', minPoints: 100, maxPoints: 299 },
  { level: 3, title: '勤学生', minPoints: 300, maxPoints: 599 },
  { level: 4, title: '优等生', minPoints: 600, maxPoints: 999 },
  { level: 5, title: '学霸', minPoints: 1000, maxPoints: 1999 },
  { level: 6, title: '学神', minPoints: 2000, maxPoints: 3999 },
  { level: 7, title: '字词大师', minPoints: 4000, maxPoints: 7999 },
  { level: 8, title: '语文天才', minPoints: 8000, maxPoints: 15999 },
  { level: 9, title: '文字精灵', minPoints: 16000, maxPoints: 31999 },
  { level: 10, title: '小神童', minPoints: 32000, maxPoints: 999999 }
];

// 称号升级规则
const titleUpgradeRules = {
  // 自动升级条件
  autoUpgrade: true,
  // 升级通知
  notification: true,
  // 升级奖励
  rewards: {
    points: 50,  // 额外积分奖励
    badge: true  // 获得专属徽章
  }
};
```

## 4. 界面设计要求

### 4.1 登录页面设计
```
┌─────────────────────────────────────────┐
│                登录页面                  │
├─────────────────────────────────────────┤
│                                         │
│            [APP LOGO]                   │
│         小学字词听写软件                  │
│                                         │
│        ╭─────────────────────╮          │
│        │      快速体验        │          │
│        ╰─────────────────────╯          │
│                                         │
│        ╭─────────────────────╮          │
│        │   [微信图标] 微信登录  │          │
│        ╰─────────────────────╯          │
│                                         │
│              [隐私政策链接]               │
│                                         │
└─────────────────────────────────────────┘
```

### 4.2 个人中心页面设计
```
┌─────────────────────────────────────────┐
│              个人中心                    │
├─────────────────────────────────────────┤
│ ╭───╮ 小明同学          [设置] [分享]     │
│ │头像│ 勤学生 ⭐⭐⭐                    │
│ ╰───╯ 1,250积分                        │
│                                         │
│ ┌─ 学习统计 ────────────────────────┐   │
│ │ 总练习次数    已练字词    正确率     │   │
│ │    156         892       85%      │   │
│ │                                   │   │
│ │ 连续学习天数  本周练习时间          │   │
│ │     15天        2小时30分         │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 我的成就 ────────────────────────┐   │
│ │ [勤学] [百发百中] [坚持不懈] [+5]   │   │
│ └───────────────────────────────────┘   │
│                                         │
│ ┌─ 功能入口 ────────────────────────┐   │
│ │ [学习报告] [错题本] [成就中心]     │   │
│ │ [设置]    [帮助]   [关于]        │   │
│ └───────────────────────────────────┘   │
└─────────────────────────────────────────┘
```

### 4.3 信息编辑页面设计
```
┌─────────────────────────────────────────┐
│          编辑个人信息                    │
├─────────────────────────────────────────┤
│                                         │
│    头像设置                             │
│    ╭───╮  [更换头像]                    │
│    │头像│                              │
│    ╰───╯                              │
│                                         │
│    昵称                                 │
│    ┌─────────────────────┐              │
│    │ 小明同学             │              │
│    └─────────────────────┘              │
│                                         │
│    年级                                 │
│    ┌─────────────────────┐              │
│    │ 三年级      [选择] │              │
│    └─────────────────────┘              │
│                                         │
│    学校                                 │
│    ┌─────────────────────┐              │
│    │ 春风小学             │              │
│    └─────────────────────┘              │
│                                         │
│    班级                                 │
│    ┌─────────────────────┐              │
│    │ 三年级二班           │              │
│    └─────────────────────┘              │
│                                         │
│    ╭─────────────────────╮              │
│    │        保存          │              │
│    ╰─────────────────────╯              │
│                                         │
└─────────────────────────────────────────┘
```

### 4.4 称号展示页面设计
```
┌─────────────────────────────────────────┐
│              我的称号                    │
├─────────────────────────────────────────┤
│                                         │
│         当前称号                        │
│      ╭─────────────╮                    │
│      │  ⭐ 勤学生 ⭐  │                    │
│      ╰─────────────╯                    │
│        1,250积分                        │
│                                         │
│  ┌─────────────────────────────────┐    │
│  │ ████████████░░░░░ 75%           │    │
│  │ 距离下一级还需250积分             │    │
│  └─────────────────────────────────┘    │
│                                         │
│         称号历史                        │
│  ┌─────────────────────────────────┐    │
│  │ 🏆 小学徒   (已获得)             │    │
│  │ 🏆 初学者   (已获得)             │    │
│  │ ⭐ 勤学生   (当前)               │    │
│  │ 🔒 优等生   (未解锁)             │    │
│  │ 🔒 学霸     (未解锁)             │    │
│  └─────────────────────────────────┘    │
│                                         │
│         特殊称号                        │
│  ┌─────────────────────────────────┐    │
│  │ 🎯 百发百中  (特殊成就获得)       │    │
│  │ 📅 坚持不懈  (连续学习15天)       │    │
│  └─────────────────────────────────┘    │
│                                         │
└─────────────────────────────────────────┘
```

## 5. 技术实现

### 5.1 前端实现（微信小程序）

#### 5.1.1 登录页面组件
```javascript
// pages/login/login.js
Page({
  data: {
    isLoggedIn: false,
    userInfo: null
  },

  onLoad() {
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    if (token) {
      this.navigateToMain();
    }
  },

  // 游客体验
  onGuestTry() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式下无法保存学习记录，建议登录后使用',
      confirmText: '继续体验',
      cancelText: '立即登录',
      success: (res) => {
        if (res.confirm) {
          wx.setStorageSync('isGuest', true);
          this.navigateToMain();
        } else {
          this.onWechatLogin();
        }
      }
    });
  },

  // 微信登录
  async onWechatLogin() {
    try {
      wx.showLoading({ title: '登录中...' });
      
      // 获取登录凭证
      const loginRes = await this.wxLogin();
      
      // 发送到后端验证
      const result = await this.loginRequest(loginRes.code);
      
      if (result.success) {
        // 保存登录状态
        wx.setStorageSync('token', result.data.token);
        wx.setStorageSync('userInfo', result.data.userInfo);
        
        // 检查是否需要完善信息
        if (!result.data.userInfo.nickname) {
          this.getUserProfile();
        } else {
          this.navigateToMain();
        }
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取用户信息
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: async (res) => {
        try {
          const updateResult = await this.updateUserInfo(res.userInfo);
          if (updateResult.success) {
            wx.setStorageSync('userInfo', updateResult.data);
            this.navigateToMain();
          }
        } catch (error) {
          console.error('更新用户信息失败:', error);
          this.navigateToMain(); // 即使更新失败也允许进入
        }
      },
      fail: () => {
        this.navigateToMain(); // 用户拒绝授权也允许进入
      }
    });
  },

  // 微信登录Promise封装
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 登录请求
  loginRequest(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiUrl}/auth/login`,
        method: 'POST',
        data: { code },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error('网络请求失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiUrl}/user/update`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: userInfo,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error('更新失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 跳转到主页
  navigateToMain() {
    wx.reLaunch({
      url: '/pages/home/<USER>'
    });
  }
});
```

#### 5.1.2 个人中心页面组件
```javascript
// pages/profile/profile.js
Page({
  data: {
    userInfo: {},
    userStats: {},
    userAchievements: [],
    isLoading: true
  },

  onLoad() {
    this.loadUserData();
  },

  onShow() {
    this.loadUserStats();
  },

  // 加载用户数据
  async loadUserData() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userInfo });
      }
      
      // 从服务器获取最新数据
      const [statsRes, achievementsRes] = await Promise.all([
        this.getUserStats(),
        this.getUserAchievements()
      ]);
      
      this.setData({
        userStats: statsRes.data,
        userAchievements: achievementsRes.data,
        isLoading: false
      });
    } catch (error) {
      console.error('加载用户数据失败:', error);
      this.setData({ isLoading: false });
    }
  },

  // 获取用户统计信息
  getUserStats() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiUrl}/user/stats`,
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error('获取统计信息失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 获取用户成就
  getUserAchievements() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiUrl}/user/achievements`,
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error('获取成就失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 编辑个人信息
  onEditProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit'
    });
  },

  // 查看称号
  onViewTitle() {
    wx.navigateTo({
      url: '/pages/profile/title'
    });
  },

  // 查看成就中心
  onViewAchievements() {
    wx.navigateTo({
      url: '/pages/achievement/list'
    });
  },

  // 分享功能
  onShareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 设置页面
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  }
});
```

### 5.2 后端实现（Node.js）

#### 5.2.1 用户认证服务
```javascript
// services/authService.js
const axios = require('axios');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const UserProfile = require('../models/UserProfile');

class AuthService {
  // 微信登录
  async wechatLogin(code) {
    try {
      // 1. 向微信服务器验证code
      const wxResponse = await this.getWxSession(code);
      
      if (!wxResponse.openid) {
        throw new Error('微信登录失败');
      }

      // 2. 查找或创建用户
      let user = await User.findOne({ openid: wxResponse.openid });
      
      if (!user) {
        user = await this.createNewUser(wxResponse);
      } else {
        // 更新最后登录时间
        await user.update({ last_login_at: new Date() });
      }

      // 3. 生成JWT token
      const token = this.generateToken(user);

      // 4. 获取用户档案
      const userProfile = await UserProfile.findOne({ user_id: user.id });

      return {
        success: true,
        data: {
          token,
          userInfo: {
            id: user.id,
            openid: user.openid,
            nickname: user.nickname,
            avatar_url: user.avatar_url,
            current_level: userProfile?.current_level || 1,
            total_points: userProfile?.total_points || 0,
            title: userProfile?.title || '小学徒'
          }
        }
      };
    } catch (error) {
      console.error('微信登录错误:', error);
      return {
        success: false,
        message: error.message || '登录失败'
      };
    }
  }

  // 获取微信session
  async getWxSession(code) {
    const response = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: process.env.WX_APPID,
        secret: process.env.WX_APP_SECRET,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });

    if (response.data.errcode) {
      throw new Error(`微信API错误: ${response.data.errmsg}`);
    }

    return response.data;
  }

  // 创建新用户
  async createNewUser(wxData) {
    const user = await User.create({
      openid: wxData.openid,
      unionid: wxData.unionid,
      nickname: `用户${wxData.openid.slice(-6)}`,
      status: 1,
      created_at: new Date(),
      last_login_at: new Date()
    });

    // 创建用户档案
    await UserProfile.create({
      user_id: user.id,
      current_level: 1,
      total_points: 0,
      title: '小学徒',
      created_at: new Date()
    });

    return user;
  }

  // 生成JWT token
  generateToken(user) {
    return jwt.sign(
      { 
        userId: user.id, 
        openid: user.openid 
      },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );
  }

  // 验证token
  verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new Error('Token验证失败');
    }
  }

  // 更新用户信息
  async updateUserInfo(userId, userInfo) {
    try {
      const updateData = {
        nickname: userInfo.nickName,
        avatar_url: userInfo.avatarUrl,
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        country: userInfo.country,
        updated_at: new Date()
      };

      await User.update(updateData, {
        where: { id: userId }
      });

      const updatedUser = await User.findByPk(userId);
      const userProfile = await UserProfile.findOne({ 
        where: { user_id: userId } 
      });

      return {
        success: true,
        data: {
          id: updatedUser.id,
          nickname: updatedUser.nickname,
          avatar_url: updatedUser.avatar_url,
          current_level: userProfile?.current_level || 1,
          total_points: userProfile?.total_points || 0,
          title: userProfile?.title || '小学徒'
        }
      };
    } catch (error) {
      console.error('更新用户信息错误:', error);
      return {
        success: false,
        message: '更新失败'
      };
    }
  }
}

module.exports = new AuthService();
```

#### 5.2.2 称号服务
```javascript
// services/titleService.js
const UserProfile = require('../models/UserProfile');

class TitleService {
  constructor() {
    this.titleLevels = [
      { level: 1, title: '小学徒', minPoints: 0, maxPoints: 99 },
      { level: 2, title: '初学者', minPoints: 100, maxPoints: 299 },
      { level: 3, title: '勤学生', minPoints: 300, maxPoints: 599 },
      { level: 4, title: '优等生', minPoints: 600, maxPoints: 999 },
      { level: 5, title: '学霸', minPoints: 1000, maxPoints: 1999 },
      { level: 6, title: '学神', minPoints: 2000, maxPoints: 3999 },
      { level: 7, title: '字词大师', minPoints: 4000, maxPoints: 7999 },
      { level: 8, title: '语文天才', minPoints: 8000, maxPoints: 15999 },
      { level: 9, title: '文字精灵', minPoints: 16000, maxPoints: 31999 },
      { level: 10, title: '小神童', minPoints: 32000, maxPoints: 999999 }
    ];
  }

  // 根据积分计算称号
  calculateTitle(totalPoints) {
    for (let i = this.titleLevels.length - 1; i >= 0; i--) {
      const level = this.titleLevels[i];
      if (totalPoints >= level.minPoints) {
        return level;
      }
    }
    return this.titleLevels[0];
  }

  // 检查并更新用户称号
  async checkAndUpdateTitle(userId, newPoints) {
    try {
      const userProfile = await UserProfile.findOne({
        where: { user_id: userId }
      });

      if (!userProfile) {
        throw new Error('用户档案不存在');
      }

      const currentTitle = this.calculateTitle(userProfile.total_points);
      const newTitle = this.calculateTitle(newPoints);

      let isUpgraded = false;
      let upgradeData = null;

      // 检查是否升级
      if (newTitle.level > currentTitle.level) {
        isUpgraded = true;
        upgradeData = {
          oldTitle: currentTitle,
          newTitle: newTitle,
          pointsEarned: newPoints - userProfile.total_points
        };

        // 更新用户档案
        await userProfile.update({
          total_points: newPoints,
          current_level: newTitle.level,
          title: newTitle.title,
          updated_at: new Date()
        });
      } else {
        // 只更新积分
        await userProfile.update({
          total_points: newPoints,
          updated_at: new Date()
        });
      }

      return {
        success: true,
        isUpgraded,
        upgradeData,
        currentTitle: newTitle
      };
    } catch (error) {
      console.error('更新称号错误:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // 获取称号进度
  getTitleProgress(totalPoints) {
    const currentTitle = this.calculateTitle(totalPoints);
    const nextTitle = this.titleLevels.find(t => t.level > currentTitle.level);

    if (!nextTitle) {
      return {
        currentTitle,
        progress: 100,
        pointsToNext: 0,
        isMaxLevel: true
      };
    }

    const pointsInCurrentLevel = totalPoints - currentTitle.minPoints;
    const pointsNeededForLevel = nextTitle.minPoints - currentTitle.minPoints;
    const progress = Math.round((pointsInCurrentLevel / pointsNeededForLevel) * 100);

    return {
      currentTitle,
      nextTitle,
      progress,
      pointsToNext: nextTitle.minPoints - totalPoints,
      isMaxLevel: false
    };
  }

  // 获取所有称号列表
  getAllTitles() {
    return this.titleLevels;
  }
}

module.exports = new TitleService();
```

## 6. 数据接口

### 6.1 登录接口
```javascript
POST /api/auth/login
Request:
{
  "code": "微信登录code"
}

Response:
{
  "success": true,
  "data": {
    "token": "JWT_TOKEN",
    "userInfo": {
      "id": 1,
      "openid": "xxx",
      "nickname": "小明",
      "avatar_url": "https://...",
      "current_level": 3,
      "total_points": 450,
      "title": "勤学生"
    }
  }
}
```

### 6.2 用户信息更新接口
```javascript
POST /api/user/update
Request:
{
  "nickName": "小明同学",
  "avatarUrl": "https://...",
  "gender": 1,
  "city": "北京",
  "province": "北京",
  "country": "中国"
}

Response:
{
  "success": true,
  "data": {
    "id": 1,
    "nickname": "小明同学",
    "avatar_url": "https://...",
    "current_level": 3,
    "total_points": 450,
    "title": "勤学生"
  }
}
```

### 6.3 用户统计接口
```javascript
GET /api/user/stats
Response:
{
  "success": true,
  "data": {
    "totalPracticeCount": 156,
    "totalWordsPracticed": 892,
    "accuracyRate": 85.6,
    "consecutiveDays": 15,
    "weeklyPracticeTime": 150,
    "totalPracticeTime": 2580
  }
}
```

### 6.4 称号信息接口
```javascript
GET /api/user/title
Response:
{
  "success": true,
  "data": {
    "currentTitle": {
      "level": 3,
      "title": "勤学生",
      "minPoints": 300,
      "maxPoints": 599
    },
    "progress": 75,
    "pointsToNext": 149,
    "nextTitle": {
      "level": 4,
      "title": "优等生",
      "minPoints": 600
    },
    "allTitles": [...],
    "isMaxLevel": false
  }
}
```

## 7. 测试用例

### 7.1 登录功能测试
```javascript
describe('用户登录功能', () => {
  test('微信登录成功', async () => {
    const mockCode = 'test_wx_code';
    const result = await authService.wechatLogin(mockCode);
    
    expect(result.success).toBe(true);
    expect(result.data.token).toBeDefined();
    expect(result.data.userInfo.id).toBeDefined();
  });

  test('无效code登录失败', async () => {
    const invalidCode = 'invalid_code';
    const result = await authService.wechatLogin(invalidCode);
    
    expect(result.success).toBe(false);
    expect(result.message).toBeDefined();
  });

  test('首次登录创建用户档案', async () => {
    const mockCode = 'new_user_code';
    const result = await authService.wechatLogin(mockCode);
    
    expect(result.data.userInfo.title).toBe('小学徒');
    expect(result.data.userInfo.total_points).toBe(0);
  });
});
```

### 7.2 称号系统测试
```javascript
describe('称号系统', () => {
  test('积分升级称号', async () => {
    const userId = 1;
    const newPoints = 350;
    
    const result = await titleService.checkAndUpdateTitle(userId, newPoints);
    
    expect(result.success).toBe(true);
    expect(result.isUpgraded).toBe(true);
    expect(result.currentTitle.title).toBe('勤学生');
  });

  test('计算称号进度', () => {
    const progress = titleService.getTitleProgress(450);
    
    expect(progress.currentTitle.title).toBe('勤学生');
    expect(progress.progress).toBeGreaterThan(0);
    expect(progress.pointsToNext).toBeGreaterThan(0);
  });

  test('最高等级处理', () => {
    const progress = titleService.getTitleProgress(50000);
    
    expect(progress.currentTitle.title).toBe('小神童');
    expect(progress.isMaxLevel).toBe(true);
    expect(progress.pointsToNext).toBe(0);
  });
});
```

### 7.3 信息更新测试
```javascript
describe('用户信息更新', () => {
  test('更新昵称成功', async () => {
    const userId = 1;
    const userInfo = {
      nickName: '新昵称',
      avatarUrl: 'https://example.com/avatar.jpg'
    };
    
    const result = await authService.updateUserInfo(userId, userInfo);
    
    expect(result.success).toBe(true);
    expect(result.data.nickname).toBe('新昵称');
  });

  test('昵称长度验证', async () => {
    const userId = 1;
    const userInfo = {
      nickName: '这是一个超过二十个字符的非常长的昵称测试'
    };
    
    const result = await authService.updateUserInfo(userId, userInfo);
    
    expect(result.success).toBe(false);
    expect(result.message).toContain('昵称长度');
  });
});
``` 
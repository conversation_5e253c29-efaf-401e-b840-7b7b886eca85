module.exports = (function() {
var __MODS__ = {};
var __DEFINE__ = function(modId, func, req) { var m = { exports: {}, _tempexports: {} }; __MODS__[modId] = { status: 0, func: func, req: req, m: m }; };
var __REQUIRE__ = function(modId, source) { if(!__MODS__[modId]) return require(source); if(!__MODS__[modId].status) { var m = __MODS__[modId].m; m._exports = m._tempexports; var desp = Object.getOwnPropertyDescriptor(m, "exports"); if (desp && desp.configurable) Object.defineProperty(m, "exports", { set: function (val) { if(typeof val === "object" && val !== m._exports) { m._exports.__proto__ = val.__proto__; Object.keys(val).forEach(function (k) { m._exports[k] = val[k]; }); } m._tempexports = val }, get: function () { return m._tempexports; } }); __MODS__[modId].status = 1; __MODS__[modId].func(__MODS__[modId].req, m, m.exports); } return __MODS__[modId].m.exports; };
var __REQUIRE_WILDCARD__ = function(obj) { if(obj && obj.__esModule) { return obj; } else { var newObj = {}; if(obj != null) { for(var k in obj) { if (Object.prototype.hasOwnProperty.call(obj, k)) newObj[k] = obj[k]; } } newObj.default = obj; return newObj; } };
var __REQUIRE_DEFAULT__ = function(obj) { return obj && obj.__esModule ? obj.default : obj; };
__DEFINE__(1748932288066, function(require, module, exports) {
/*!
 * Hanzi Writer v2.3.0
 * https://chanind.github.io/hanzi-writer
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["HanziWriter"] = factory();
	else
		root["HanziWriter"] = factory();
})(typeof self !== 'undefined' ? self : this, function() {
return /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			});
/******/ 		}
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 9);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; };

function emptyFunc() {}

var performanceNow = global.performance && function () {
  return global.performance.now();
} || function () {
  return Date.now();
};
var requestAnimationFrame = global.requestAnimationFrame || function (callback) {
  return setTimeout(function () {
    return callback(performanceNow());
  }, 1000 / 60);
};
var cancelAnimationFrame = global.cancelAnimationFrame || clearTimeout;

// Object.assign polyfill, because IE :/
var _assign = function _assign(target) {
  var overrideTarget = Object(target);

  for (var _len = arguments.length, overrides = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    overrides[_key - 1] = arguments[_key];
  }

  overrides.forEach(function (override) {
    if (override != null) {
      for (var key in override) {
        if (Object.prototype.hasOwnProperty.call(override, key)) {
          overrideTarget[key] = override[key];
        }
      }
    }
  });
  return overrideTarget;
};

var assign = Object.assign || _assign;

var arrLast = function arrLast(arr) {
  return arr[arr.length - 1];
};

function copyAndMergeDeep(base, override) {
  var output = assign({}, base);
  for (var key in override) {
    // eslint-disable-line guard-for-in
    // skipping hasOwnProperty check for performance reasons - we shouldn't be passing any objects
    // in here that aren't plain objects anyway and this is a hot code path
    var baseVal = base[key];
    var overrideVal = override[key];
    if (baseVal === overrideVal) continue; // eslint-disable-line no-continue
    if (baseVal && overrideVal && (typeof baseVal === 'undefined' ? 'undefined' : _typeof(baseVal)) === 'object' && (typeof overrideVal === 'undefined' ? 'undefined' : _typeof(overrideVal)) === 'object' && !Array.isArray(overrideVal)) {
      output[key] = copyAndMergeDeep(baseVal, overrideVal);
    } else {
      output[key] = overrideVal;
    }
  }
  return output;
}

function inflate(scope, obj) {
  var parts = scope.split('.');
  var final = {};
  var current = final;
  for (var i = 0; i < parts.length; i++) {
    var cap = i === parts.length - 1 ? obj : {};
    current[parts[i]] = cap;
    current = cap;
  }
  return final;
}

function callIfExists(callback, arg) {
  if (callback) callback(arg);
  return arg;
}

var count = 0;
function counter() {
  count++;
  return count;
}

function average(arr) {
  var sum = arr.reduce(function (acc, val) {
    return val + acc;
  }, 0);
  return sum / arr.length;
}

function timeout() {
  var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;

  return new Promise(function (resolve, reject) {
    setTimeout(resolve, duration);
  });
}

function colorStringToVals(colorString) {
  var normalizedColor = colorString.toUpperCase().trim();
  // based on https://stackoverflow.com/a/21648508
  if (/^#([A-F0-9]{3}){1,2}$/.test(normalizedColor)) {
    var hexParts = normalizedColor.substring(1).split('');
    if (hexParts.length === 3) {
      hexParts = [hexParts[0], hexParts[0], hexParts[1], hexParts[1], hexParts[2], hexParts[2]];
    }
    var hexStr = '' + hexParts.join('');
    return {
      r: parseInt(hexStr.slice(0, 2), 16),
      g: parseInt(hexStr.slice(2, 4), 16),
      b: parseInt(hexStr.slice(4, 6), 16),
      a: 1
    };
  }
  var rgbMatch = normalizedColor.match(/^RGBA?\((\d+)\s*,\s*(\d+)\s*,\s*(\d+)(?:\s*,\s*(\d*\.?\d+))?\)$/);
  if (rgbMatch) {
    return {
      r: parseInt(rgbMatch[1], 10),
      g: parseInt(rgbMatch[2], 10),
      b: parseInt(rgbMatch[3], 10),
      a: parseFloat(rgbMatch[4] || 1, 10)
    };
  }
  throw new Error('Invalid color: ' + colorString);
}

var trim = function trim(string) {
  return string.replace(/^\s+/, '').replace(/\s+$/, '');
};

// return a new array-like object with int keys where each key is item
// ex: objRepeat({x: 8}, 3) === {0: {x: 8}, 1: {x: 8}, 2: {x: 8}}
var objRepeat = function objRepeat(item, times) {
  var obj = {};
  for (var i = 0; i < times; i++) {
    obj[i] = item;
  }
  return obj;
};

var ua = global.navigator && global.navigator.userAgent || '';
var isMsBrowser = ua.indexOf('MSIE ') > 0 || ua.indexOf('Trident/') > 0 || ua.indexOf('Edge/') > 0;

module.exports = {
  _assign: _assign,
  arrLast: arrLast,
  assign: assign,
  average: average,
  callIfExists: callIfExists,
  cancelAnimationFrame: cancelAnimationFrame,
  colorStringToVals: colorStringToVals,
  copyAndMergeDeep: copyAndMergeDeep,
  counter: counter,
  emptyFunc: emptyFunc,
  inflate: inflate,
  objRepeat: objRepeat,
  performanceNow: performanceNow,
  requestAnimationFrame: requestAnimationFrame,
  timeout: timeout,
  trim: trim,
  isMsBrowser: isMsBrowser
};
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 1 */
/***/ (function(module, exports) {

var g;

// This works in non-strict mode
g = (function() {
	return this;
})();

try {
	// This works if eval is allowed (see CSP)
	g = g || Function("return this")() || (1,eval)("this");
} catch(e) {
	// This works if the window reference is available
	if(typeof window === "object")
		g = window;
}

// g can still be undefined, but nothing to do about it...
// We return undefined, instead of nothing here, so it's
// easier to handle this case. if(!global) { ...}

module.exports = g;


/***/ }),
/* 2 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    average = _require.average,
    arrLast = _require.arrLast;

var subtract = function subtract(p1, p2) {
  return { x: p1.x - p2.x, y: p1.y - p2.y };
};
var magnitude = function magnitude(point) {
  return Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));
};
var distance = function distance(point1, point2) {
  return magnitude(subtract(point1, point2));
};
var equals = function equals(point1, point2) {
  return point1.x === point2.x && point1.y === point2.y;
};
var round = function round(point) {
  var precision = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;

  var multiplier = precision * 10;
  return {
    x: Math.round(multiplier * point.x) / multiplier,
    y: Math.round(multiplier * point.y) / multiplier
  };
};

var length = function length(points) {
  var lastPoint = points[0];
  var pointsSansFirst = points.slice(1);
  return pointsSansFirst.reduce(function (acc, point) {
    var dist = distance(point, lastPoint);
    lastPoint = point;
    return acc + dist;
  }, 0);
};

var cosineSimilarity = function cosineSimilarity(point1, point2) {
  var rawDotProduct = point1.x * point2.x + point1.y * point2.y;
  return rawDotProduct / magnitude(point1) / magnitude(point2);
};

// return a new point, p3, which is on the same line as p1 and p2, but distance away
// from p2. p1, p2, p3 will always lie on the line in that order
var _extendPointOnLine = function _extendPointOnLine(p1, p2, dist) {
  var vect = subtract(p2, p1);
  var norm = dist / magnitude(vect);
  return { x: p2.x + norm * vect.x, y: p2.y + norm * vect.y };
};

// based on http://www.kr.tuwien.ac.at/staff/eiter/et-archive/cdtr9464.pdf
var frechetDist = function frechetDist(curve1, curve2) {
  var results = [];
  for (var i = 0; i < curve1.length; i++) {
    results.push([]);
    for (var j = 0; j < curve2.length; j++) {
      results[i].push(-1);
    }
  }

  var recursiveCalc = function recursiveCalc(i, j) {
    if (results[i][j] > -1) return results[i][j];
    if (i === 0 && j === 0) {
      results[i][j] = distance(curve1[0], curve2[0]);
    } else if (i > 0 && j === 0) {
      results[i][j] = Math.max(recursiveCalc(i - 1, 0), distance(curve1[i], curve2[0]));
    } else if (i === 0 && j > 0) {
      results[i][j] = Math.max(recursiveCalc(0, j - 1), distance(curve1[0], curve2[j]));
    } else if (i > 0 && j > 0) {
      results[i][j] = Math.max(Math.min(recursiveCalc(i - 1, j), recursiveCalc(i - 1, j - 1), recursiveCalc(i, j - 1)), distance(curve1[i], curve2[j]));
    } else {
      results[i][j] = Infinity;
    }
    return results[i][j];
  };

  return recursiveCalc(curve1.length - 1, curve2.length - 1);
};

// break up long segments in the curve into smaller segments of len maxLen or smaller
var subdivideCurve = function subdivideCurve(curve) {
  var maxLen = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.05;

  var newCurve = curve.slice(0, 1);
  curve.slice(1).forEach(function (point) {
    var prevPoint = newCurve[newCurve.length - 1];
    var segLen = distance(point, prevPoint);
    if (segLen > maxLen) {
      var numNewPoints = Math.ceil(segLen / maxLen);
      var newSegLen = segLen / numNewPoints;
      for (var i = 0; i < numNewPoints; i++) {
        newCurve.push(_extendPointOnLine(point, prevPoint, -1 * newSegLen * (i + 1)));
      }
    } else {
      newCurve.push(point);
    }
  });
  return newCurve;
};

// redraw the curve using numPoints equally spaced out along the length of the curve
var outlineCurve = function outlineCurve(curve) {
  var numPoints = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 30;

  var curveLen = length(curve);
  var segmentLen = curveLen / (numPoints - 1);
  var outlinePoints = [curve[0]];
  var endPoint = arrLast(curve);
  var remainingCurvePoints = curve.slice(1);
  for (var i = 0; i < numPoints - 2; i++) {
    var lastPoint = arrLast(outlinePoints);
    var remainingDist = segmentLen;
    var outlinePointFound = false;
    while (!outlinePointFound) {
      var nextPointDist = distance(lastPoint, remainingCurvePoints[0]);
      if (nextPointDist < remainingDist) {
        remainingDist -= nextPointDist;
        lastPoint = remainingCurvePoints.shift();
      } else {
        var nextPoint = _extendPointOnLine(lastPoint, remainingCurvePoints[0], remainingDist - nextPointDist);
        outlinePoints.push(nextPoint);
        outlinePointFound = true;
      }
    }
  }
  outlinePoints.push(endPoint);
  return outlinePoints;
};

// translate and scale from https://en.wikipedia.org/wiki/Procrustes_analysis
var normalizeCurve = function normalizeCurve(curve) {
  var outlinedCurve = outlineCurve(curve);
  var meanX = average(outlinedCurve.map(function (point) {
    return point.x;
  }));
  var meanY = average(outlinedCurve.map(function (point) {
    return point.y;
  }));
  var mean = { x: meanX, y: meanY };
  var translatedCurve = outlinedCurve.map(function (point) {
    return subtract(point, mean);
  });
  var scale = Math.sqrt(average([Math.pow(translatedCurve[0].x, 2) + Math.pow(translatedCurve[0].y, 2), Math.pow(arrLast(translatedCurve).x, 2) + Math.pow(arrLast(translatedCurve).y, 2)]));
  var scaledCurve = translatedCurve.map(function (point) {
    return { x: point.x / scale, y: point.y / scale };
  });
  return subdivideCurve(scaledCurve);
};

// rotate around the origin
var rotate = function rotate(curve, theta) {
  return curve.map(function (point) {
    return {
      x: Math.cos(theta) * point.x - Math.sin(theta) * point.y,
      y: Math.sin(theta) * point.x + Math.cos(theta) * point.y
    };
  });
};

// remove intermediate points that are on the same line as the points to either side
var _filterParallelPoints = function _filterParallelPoints(points) {
  if (points.length < 3) return points;
  var filteredPoints = [points[0], points[1]];
  points.slice(2).forEach(function (point, i) {
    var numFilteredPoints = filteredPoints.length;
    var curVect = subtract(point, filteredPoints[numFilteredPoints - 1]);
    var prevVect = subtract(filteredPoints[numFilteredPoints - 1], filteredPoints[numFilteredPoints - 2]);
    // this is the z coord of the cross-product. If this is 0 then they're parallel
    var isParallel = curVect.y * prevVect.x - curVect.x * prevVect.y === 0;
    if (isParallel) {
      filteredPoints.pop();
    }
    filteredPoints.push(point);
  });
  return filteredPoints;
};

function getPathString(points) {
  var close = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;

  var start = round(points[0]);
  var remainingPoints = points.slice(1);
  var pathString = 'M ' + start.x + ' ' + start.y;
  remainingPoints.forEach(function (point) {
    var roundedPoint = round(point);
    pathString += ' L ' + roundedPoint.x + ' ' + roundedPoint.y;
  });
  if (close) pathString += 'Z';
  return pathString;
}

// take points on a path and move their start point backwards by distance
var extendStart = function extendStart(points, dist) {
  var filteredPoints = _filterParallelPoints(points);
  if (filteredPoints.length < 2) return filteredPoints;
  var p1 = filteredPoints[1];
  var p2 = filteredPoints[0];
  var newStart = _extendPointOnLine(p1, p2, dist);
  var extendedPoints = filteredPoints.slice(1);
  extendedPoints.unshift(newStart);
  return extendedPoints;
};

module.exports = {
  round: round,
  equals: equals,
  distance: distance,
  getPathString: getPathString,
  frechetDist: frechetDist,
  length: length,
  rotate: rotate,
  subtract: subtract,
  extendStart: extendStart,
  cosineSimilarity: cosineSimilarity,
  outlineCurve: outlineCurve,
  _extendPointOnLine: _extendPointOnLine,
  _filterParallelPoints: _filterParallelPoints,
  subdivideCurve: subdivideCurve,
  normalizeCurve: normalizeCurve
};

/***/ }),
/* 3 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

function createElm(elmType) {
  return global.document.createElementNS('http://www.w3.org/2000/svg', elmType);
}

function attr(elm, name, value) {
  elm.setAttributeNS(null, name, value);
}

function attrs(elm, attrsMap) {
  Object.keys(attrsMap).forEach(function (attrName) {
    return attr(elm, attrName, attrsMap[attrName]);
  });
}

// inspired by https://talk.observablehq.com/t/hanzi-writer-renders-incorrectly-inside-an-observable-notebook-on-a-mobile-browser/1898
function urlIdRef(id) {
  var prefix = '';
  if (global.location && global.location.href) {
    prefix = global.location.href.replace(/#[^#]*$/, '');
  }
  return 'url(' + prefix + '#' + id + ')';
}

function removeElm(elm) {
  if (elm) elm.parentNode.removeChild(elm);
}

module.exports = { createElm: createElm, attrs: attrs, attr: attr, removeElm: removeElm, urlIdRef: urlIdRef };
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 4 */
/***/ (function(module, exports, __webpack_require__) {




function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var Mutation = __webpack_require__(5);

var _require = __webpack_require__(0),
    objRepeat = _require.objRepeat;

var showStrokes = function showStrokes(charName, character, duration) {
  return [new Mutation('character.' + charName + '.strokes', objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length), { duration: duration, force: true })];
};

var showCharacter = function showCharacter(charName, character, duration) {
  return [new Mutation('character.' + charName, {
    opacity: 1,
    strokes: objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length)
  }, { duration: duration, force: true })];
};

var hideCharacter = function hideCharacter(charName, character, duration) {
  return [new Mutation('character.' + charName + '.opacity', 0, { duration: duration, force: true })].concat(showStrokes(charName, character, 0));
};

var updateColor = function updateColor(colorName, colorVal, duration) {
  return [new Mutation('options.' + colorName, colorVal, { duration: duration })];
};

var highlightStroke = function highlightStroke(stroke, color, speed) {
  var strokeNum = stroke.strokeNum;
  var duration = (stroke.getLength() + 600) / (3 * speed);
  return [new Mutation('character.highlight.strokeColor', color), new Mutation('character.highlight', {
    opacity: 1,
    strokes: _defineProperty({}, strokeNum, {
      displayPortion: 0,
      opacity: 0
    })
  }), new Mutation('character.highlight.strokes.' + strokeNum, {
    displayPortion: 1,
    opacity: 1
  }, { duration: duration }), new Mutation('character.highlight.strokes.' + strokeNum + '.opacity', 0, { duration: duration })];
};

var animateStroke = function animateStroke(charName, stroke, speed) {
  var strokeNum = stroke.strokeNum;
  var duration = (stroke.getLength() + 600) / (3 * speed);
  return [new Mutation('character.' + charName, {
    opacity: 1,
    strokes: _defineProperty({}, strokeNum, {
      displayPortion: 0,
      opacity: 1
    })
  }), new Mutation('character.' + charName + '.strokes.' + strokeNum + '.displayPortion', 1, { duration: duration })];
};

var animateSingleStroke = function animateSingleStroke(charName, character, strokeNum, speed) {
  var mutationStateFunc = function mutationStateFunc(state) {
    var curCharState = state.character[charName];
    var mutationState = {
      opacity: 1,
      strokes: {}
    };
    for (var i = 0; i < character.strokes.length; i++) {
      mutationState.strokes[i] = {
        opacity: curCharState.opacity * curCharState.strokes[i].opacity
      };
    }
    return mutationState;
  };
  return [new Mutation('character.' + charName, mutationStateFunc)].concat(animateStroke(charName, character.strokes[strokeNum], speed));
};

var showStroke = function showStroke(charName, strokeNum, duration) {
  return [new Mutation('character.' + charName + '.strokes.' + strokeNum, {
    displayPortion: 1,
    opacity: 1
  }, { duration: duration, force: true })];
};

var animateCharacter = function animateCharacter(charName, character, fadeDuration, speed, delayBetweenStrokes) {
  var mutations = hideCharacter(charName, character, fadeDuration);
  mutations = mutations.concat(showStrokes(charName, character, 0));
  mutations.push(new Mutation('character.' + charName, {
    opacity: 1,
    strokes: objRepeat({ opacity: 0 }, character.strokes.length)
  }, { force: true }));
  character.strokes.forEach(function (stroke, i) {
    if (i > 0) mutations.push(new Mutation.Delay(delayBetweenStrokes));
    mutations = mutations.concat(animateStroke(charName, stroke, speed));
  });
  return mutations;
};

var animateCharacterLoop = function animateCharacterLoop(charName, character, fadeDuration, speed, delayBetweenStrokes, delayBetweenLoops) {
  var mutations = animateCharacter(charName, character, fadeDuration, speed, delayBetweenStrokes);
  mutations.push(new Mutation.Delay(delayBetweenLoops));
  return mutations;
};

module.exports = {
  showStrokes: showStrokes,
  showCharacter: showCharacter,
  hideCharacter: hideCharacter,
  highlightStroke: highlightStroke,
  animateCharacter: animateCharacter,
  animateCharacterLoop: animateCharacterLoop,
  animateStroke: animateStroke,
  animateSingleStroke: animateSingleStroke,
  showStroke: showStroke,
  updateColor: updateColor
};

/***/ }),
/* 5 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    inflate = _require.inflate,
    performanceNow = _require.performanceNow,
    requestAnimationFrame = _require.requestAnimationFrame,
    cancelAnimationFrame = _require.cancelAnimationFrame;

// ------ Mutation class --------

var getPartialValues = function getPartialValues(startValues, endValues, progress) {
  var target = {};
  for (var key in endValues) {
    // eslint-disable-line guard-for-in
    // skipping hasOwnProperty check for performance reasons - we shouldn't be passing any objects
    // in here that aren't plain objects anyway and this is a hot code path
    var endValue = endValues[key];
    var startValue = startValues[key];
    if (endValue >= 0) {
      target[key] = progress * (endValue - startValue) + startValue;
    } else {
      target[key] = getPartialValues(startValue, endValue, progress);
    }
  }
  return target;
};

var isAlreadyAtEnd = function isAlreadyAtEnd(startValues, endValues) {
  for (var key in endValues) {
    if (endValues.hasOwnProperty(key)) {
      var endValue = endValues[key];
      var startValue = startValues[key];
      if (endValue >= 0) {
        if (endValue !== startValue) return false;
      } else if (!isAlreadyAtEnd(startValue, endValue)) {
        return false;
      }
    }
  }
  return true;
};

// from https://github.com/maxwellito/vivus
var ease = function ease(x) {
  return -Math.cos(x * Math.PI) / 2 + 0.5;
};

function Mutation(scope, valuesOrCallable) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};

  this.scope = scope;
  this._valuesOrCallable = valuesOrCallable;
  this._duration = options.duration || 0;
  this._force = options.force;
  this._pausedDuration = 0;
  this._tickBound = this._tick.bind(this);
  this._startPauseTime = null;
}

Mutation.prototype.run = function (renderState) {
  var _this = this;

  if (!this._values) this._inflateValues(renderState);
  if (this._duration === 0) renderState.updateState(this._values);
  if (this._duration === 0 || isAlreadyAtEnd(renderState.state, this._values)) {
    return Promise.resolve();
  }
  this._renderState = renderState;
  this._startState = renderState.state;
  this._startTime = performanceNow();
  this._frameHandle = requestAnimationFrame(this._tickBound);
  return new Promise(function (resolve) {
    _this._resolve = resolve;
  });
};

Mutation.prototype.pause = function () {
  if (this._startPauseTime !== null) return;
  if (this._frameHandle) cancelAnimationFrame(this._frameHandle);
  this._startPauseTime = performanceNow();
};

Mutation.prototype.resume = function () {
  if (this._startPauseTime === null) return;
  this._frameHandle = requestAnimationFrame(this._tickBound);
  this._pausedDuration += performanceNow() - this._startPauseTime;
  this._startPauseTime = null;
};

Mutation.prototype._tick = function (timing) {
  if (this._startPauseTime !== null) return;
  var progress = Math.min(1, (timing - this._startTime - this._pausedDuration) / this._duration);
  if (progress === 1) {
    this._renderState.updateState(this._values);
    this._frameHandle = null;
    this.cancel(this._renderState);
  } else {
    var easedProgress = ease(progress);
    this._renderState.updateState(getPartialValues(this._startState, this._values, easedProgress));
    this._frameHandle = requestAnimationFrame(this._tickBound);
  }
};

Mutation.prototype._inflateValues = function (renderState) {
  var values = this._valuesOrCallable;
  if (typeof this._valuesOrCallable === 'function') {
    values = this._valuesOrCallable(renderState.state);
  }
  this._values = inflate(this.scope, values);
};

Mutation.prototype.cancel = function (renderState) {
  if (this._resolve) this._resolve();
  this._resolve = null;
  if (this._frameHandle) cancelAnimationFrame(this._frameHandle);
  this._frameHandle = null;
  if (this._force) {
    if (!this._values) this._inflateValues(renderState);
    renderState.updateState(this._values);
  }
};

// ------ Mutation.Delay Class --------

function Delay(duration) {
  this._duration = duration;
  this._startTime = null;
  this._paused = false;
}

Delay.prototype.pause = function () {
  if (this._paused) return;
  // to pause, clear the timeout and rewrite this._duration with whatever time is remaining
  var elapsedDelay = performanceNow() - this._startTime;
  this._duration = Math.max(0, this._duration - elapsedDelay);
  clearTimeout(this._timeout);
  this._paused = true;
};

Delay.prototype.resume = function () {
  var _this2 = this;

  if (!this._paused) return;
  this._startTime = performanceNow();
  this._timeout = setTimeout(function () {
    return _this2.cancel();
  }, this._duration);
  this._paused = false;
};

Delay.prototype.run = function () {
  var _this3 = this;

  var timeoutPromise = new Promise(function (resolve) {
    _this3._resolve = resolve;
  });
  this._startTime = performanceNow();
  this._timeout = setTimeout(function () {
    return _this3.cancel();
  }, this._duration);
  return timeoutPromise;
};

Delay.prototype.cancel = function () {
  clearTimeout(this._timeout);
  if (this._resolve) this._resolve();
  this._resolve = false;
};

Mutation.Delay = Delay;

// -------------------------------------


module.exports = Mutation;

/***/ }),
/* 6 */
/***/ (function(module, exports, __webpack_require__) {




function StrokeRendererBase() {}

StrokeRendererBase.prototype._getStrokeDashoffset = function (displayPortion) {
  return this._pathLength * 0.999 * (1 - displayPortion);
};

StrokeRendererBase.prototype._getColor = function (_ref) {
  var strokeColor = _ref.strokeColor,
      radicalColor = _ref.radicalColor;

  return radicalColor && this._stroke.isInRadical ? radicalColor : strokeColor;
};

module.exports = StrokeRendererBase;

/***/ }),
/* 7 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

function RenderTargetBase() {}

RenderTargetBase.prototype.addPointerStartListener = function (callback) {
  var _this = this;

  this.node.addEventListener('mousedown', function (evt) {
    callback(_this._eventify(evt, _this._getMousePoint));
  });
  this.node.addEventListener('touchstart', function (evt) {
    callback(_this._eventify(evt, _this._getTouchPoint));
  });
};

RenderTargetBase.prototype.addPointerMoveListener = function (callback) {
  var _this2 = this;

  this.node.addEventListener('mousemove', function (evt) {
    callback(_this2._eventify(evt, _this2._getMousePoint));
  });
  this.node.addEventListener('touchmove', function (evt) {
    callback(_this2._eventify(evt, _this2._getTouchPoint));
  });
};

RenderTargetBase.prototype.addPointerEndListener = function (callback) {
  // TODO: find a way to not need global listeners
  global.document.addEventListener('mouseup', callback);
  global.document.addEventListener('touchend', callback);
};

RenderTargetBase.prototype.getBoundingClientRect = function () {
  return this.node.getBoundingClientRect();
};

RenderTargetBase.prototype._eventify = function (evt, pointFunc) {
  var _this3 = this;

  return {
    getPoint: function getPoint() {
      return pointFunc.call(_this3, evt);
    },
    preventDefault: function preventDefault() {
      return evt.preventDefault();
    }
  };
};

RenderTargetBase.prototype._getMousePoint = function (evt) {
  var box = this.getBoundingClientRect();
  var x = evt.clientX - box.left;
  var y = evt.clientY - box.top;
  return { x: x, y: y };
};

RenderTargetBase.prototype._getTouchPoint = function (evt) {
  var box = this.getBoundingClientRect();
  var x = evt.touches[0].clientX - box.left;
  var y = evt.touches[0].clientY - box.top;
  return { x: x, y: y };
};

module.exports = RenderTargetBase;
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 8 */
/***/ (function(module, exports, __webpack_require__) {




function _toArray(arr) { return Array.isArray(arr) ? arr : Array.from(arr); }

var drawPath = function drawPath(ctx, points) {
  ctx.beginPath();
  var start = points[0];
  var remainingPoints = points.slice(1);
  ctx.moveTo(start.x, start.y);
  remainingPoints.forEach(function (point) {
    ctx.lineTo(point.x, point.y);
  });
  ctx.stroke();
};

// break a path string into a series of canvas path commands
// only works with the subset of SVG paths used by MakeMeAHanzi data
var pathStringToCanvas = function pathStringToCanvas(pathString) {
  var pathParts = pathString.split(/(^|\s+)(?=[A-Z])/).filter(function (part) {
    return part !== ' ';
  });
  var commands = [function (ctx) {
    return ctx.beginPath();
  }];
  pathParts.forEach(function (part) {
    var _part$split = part.split(/\s+/),
        _part$split2 = _toArray(_part$split),
        cmd = _part$split2[0],
        rawParams = _part$split2.slice(1);

    var params = rawParams.map(function (param) {
      return parseFloat(param);
    });
    if (cmd === 'M') {
      commands.push(function (ctx) {
        return ctx.moveTo.apply(ctx, params);
      });
    } else if (cmd === 'L') {
      commands.push(function (ctx) {
        return ctx.lineTo.apply(ctx, params);
      });
    } else if (cmd === 'C') {
      commands.push(function (ctx) {
        return ctx.bezierCurveTo.apply(ctx, params);
      });
    } else if (cmd === 'Q') {
      commands.push(function (ctx) {
        return ctx.quadraticCurveTo.apply(ctx, params);
      });
    } else if (cmd === 'Z') {
      // commands.push((ctx) => ctx.closePath());
    }
  });
  return function (ctx) {
    return commands.forEach(function (cmd) {
      return cmd(ctx);
    });
  };
};

module.exports = { drawPath: drawPath, pathStringToCanvas: pathStringToCanvas };

/***/ }),
/* 9 */
/***/ (function(module, exports, __webpack_require__) {




var RenderState = __webpack_require__(10);
var parseCharData = __webpack_require__(11);
var Positioner = __webpack_require__(14);
var Quiz = __webpack_require__(15);
var svgRenderer = __webpack_require__(19);
var canvasRenderer = __webpack_require__(25);
var defaultCharDataLoader = __webpack_require__(31);
var LoadingManager = __webpack_require__(32);
var characterActions = __webpack_require__(4);

var _require = __webpack_require__(0),
    assign = _require.assign,
    callIfExists = _require.callIfExists,
    trim = _require.trim,
    colorStringToVals = _require.colorStringToVals;

var defaultOptions = {
  charDataLoader: defaultCharDataLoader,
  onLoadCharDataError: null,
  onLoadCharDataSuccess: null,
  showOutline: true,
  showCharacter: true,
  renderer: 'svg',

  // positioning options

  width: null,
  height: null,
  padding: 20,

  // animation options

  strokeAnimationSpeed: 1,
  strokeFadeDuration: 400,
  strokeHighlightDuration: 200,
  strokeHighlightSpeed: 2,
  delayBetweenStrokes: 1000,
  delayBetweenLoops: 2000,

  // colors

  strokeColor: '#555',
  radicalColor: null,
  highlightColor: '#AAF',
  outlineColor: '#DDD',
  drawingColor: '#333',

  // quiz options

  leniency: 1,
  showHintAfterMisses: 3,
  highlightOnComplete: true,
  highlightCompleteColor: null,

  // undocumented obscure options

  drawingFadeDuration: 300,
  drawingWidth: 4,
  strokeWidth: 2,
  outlineWidth: 2,
  rendererOverride: {}
};

function HanziWriter() {
  if (arguments.length > 0) {
    var character = void 0;
    var options = {};
    var element = arguments.length <= 0 ? undefined : arguments[0];
    if (arguments.length > 1) {
      if (typeof (arguments.length <= 1 ? undefined : arguments[1]) === 'string') {
        // eslint-disable-next-line
        console.warn('Using new HanziWriter() to set a character is deprecated. Use HanziWriter.create() instead');
        character = arguments.length <= 1 ? undefined : arguments[1];
        options = (arguments.length <= 2 ? undefined : arguments[2]) || {};
      } else {
        options = arguments.length <= 1 ? undefined : arguments[1];
      }
    }
    this._init(element, options);
    if (character) {
      this.setCharacter(character);
    }
  }
}

// ------ public API ------ //

HanziWriter.prototype.showCharacter = function () {
  var _this = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this._options.showCharacter = true;
  return this._withData(function () {
    return _this._renderState.run(characterActions.showCharacter('main', _this._character, typeof options.duration === 'number' ? options.duration : _this._options.strokeFadeDuration)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};
HanziWriter.prototype.hideCharacter = function () {
  var _this2 = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this._options.showCharacter = false;
  return this._withData(function () {
    return _this2._renderState.run(characterActions.hideCharacter('main', _this2._character, typeof options.duration === 'number' ? options.duration : _this2._options.strokeFadeDuration)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};
HanziWriter.prototype.animateCharacter = function () {
  var _this3 = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this.cancelQuiz();
  return this._withData(function () {
    return _this3._renderState.run(characterActions.animateCharacter('main', _this3._character, _this3._options.strokeFadeDuration, _this3._options.strokeAnimationSpeed, _this3._options.delayBetweenStrokes)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};
HanziWriter.prototype.animateStroke = function (strokeNum) {
  var _this4 = this;

  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

  this.cancelQuiz();
  return this._withData(function () {
    return _this4._renderState.run(characterActions.animateSingleStroke('main', _this4._character, strokeNum, _this4._options.strokeAnimationSpeed)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};
HanziWriter.prototype.highlightStroke = function (strokeNum) {
  var _this5 = this;

  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

  return this._withData(function () {
    return _this5._renderState.run(characterActions.highlightStroke(_this5._character.strokes[strokeNum], _this5._options.highlightColor, _this5._options.strokeHighlightSpeed)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};
HanziWriter.prototype.loopCharacterAnimation = function () {
  var _this6 = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this.cancelQuiz();
  return this._withData(function () {
    return _this6._renderState.run(characterActions.animateCharacterLoop('main', _this6._character, _this6._options.strokeFadeDuration, _this6._options.strokeAnimationSpeed, _this6._options.delayBetweenStrokes, _this6._options.delayBetweenLoops), { loop: true });
  });
};

HanziWriter.prototype.pauseAnimation = function () {
  var _this7 = this;

  return this._withData(function () {
    return _this7._renderState.pauseAll();
  });
};

HanziWriter.prototype.resumeAnimation = function () {
  var _this8 = this;

  return this._withData(function () {
    return _this8._renderState.resumeAll();
  });
};

HanziWriter.prototype.showOutline = function () {
  var _this9 = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this._options.showOutline = true;
  return this._withData(function () {
    return _this9._renderState.run(characterActions.showCharacter('outline', _this9._character, typeof options.duration === 'number' ? options.duration : _this9._options.strokeFadeDuration)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};

HanziWriter.prototype.hideOutline = function () {
  var _this10 = this;

  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this._options.showOutline = false;
  return this._withData(function () {
    return _this10._renderState.run(characterActions.hideCharacter('outline', _this10._character, typeof options.duration === 'number' ? options.duration : _this10._options.strokeFadeDuration)).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};

HanziWriter.prototype.updateColor = function (colorName, colorVal) {
  var _this11 = this;

  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};

  return this._withData(function () {
    var duration = typeof options.duration === 'number' ? options.duration : _this11._options.strokeFadeDuration;
    var fixedColorVal = colorVal;
    // If we're removing radical color, tween it to the stroke color
    if (colorName === 'radicalColor' && !colorVal) {
      fixedColorVal = _this11._options.strokeColor;
    }
    var mappedColor = colorStringToVals(fixedColorVal);
    _this11._options[colorName] = colorVal;
    var mutation = characterActions.updateColor(colorName, mappedColor, duration);
    // make sure to set radicalColor back to null after the transition finishes if val == null
    if (colorName === 'radicalColor' && !colorVal) {
      mutation = mutation.concat(characterActions.updateColor(colorName, null, 0));
    }
    return _this11._renderState.run(mutation).then(function (res) {
      return callIfExists(options.onComplete, res);
    });
  });
};

HanziWriter.prototype.quiz = function () {
  var _this12 = this;

  var quizOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

  this._withData(function () {
    _this12.cancelQuiz();
    _this12._quiz = new Quiz(_this12._character, _this12._renderState, _this12._positioner);
    _this12._quiz.startQuiz(assign({}, _this12._options, quizOptions));
  });
};

HanziWriter.prototype.cancelQuiz = function () {
  if (this._quiz) {
    this._quiz.cancel();
    this._quiz = null;
  }
};

HanziWriter.prototype.setCharacter = function (char) {
  var _this13 = this;

  this.cancelQuiz();
  this._char = char;
  if (this._hanziWriterRenderer) this._hanziWriterRenderer.destroy();
  if (this._renderState) this._renderState.cancelAll();
  this._hanziWriterRenderer = null;
  this._withDataPromise = this._loadingManager.loadCharData(char).then(function (pathStrings) {
    if (_this13._loadingManager.loadingFailed) return;

    _this13._character = parseCharData(char, pathStrings);
    _this13._positioner = new Positioner(_this13._options);
    var hanziWriterRenderer = new _this13._renderer.HanziWriterRenderer(_this13._character, _this13._positioner);
    _this13._hanziWriterRenderer = hanziWriterRenderer;
    _this13._renderState = new RenderState(_this13._character, _this13._options, function (nextState) {
      hanziWriterRenderer.render(nextState);
    });
    _this13._hanziWriterRenderer.mount(_this13.target);
    _this13._hanziWriterRenderer.render(_this13._renderState.state);
  });
  return this._withDataPromise;
};

// ------------- //

HanziWriter.prototype._init = function (element, options) {
  var renderer = options.renderer === 'canvas' ? canvasRenderer : svgRenderer;
  var rendererOverride = options.rendererOverride || {};
  this._renderer = {
    HanziWriterRenderer: rendererOverride.HanziWriterRenderer || renderer.HanziWriterRenderer,
    createRenderTarget: rendererOverride.createRenderTarget || renderer.createRenderTarget
  };
  // wechat miniprogram component needs direct access to the render target, so this is public
  this.target = this._renderer.createRenderTarget(element, options.width, options.height);
  this._options = this._assignOptions(options);
  this._loadingManager = new LoadingManager(this._options);
  this._setupListeners();
  this._quiz = null;
  return this;
};

HanziWriter.prototype._assignOptions = function (options) {
  var mergedOptions = assign({}, defaultOptions, options);

  // backfill strokeAnimationSpeed if deprecated strokeAnimationDuration is provided instead
  if (options.strokeAnimationDuration && !options.strokeAnimationSpeed) {
    mergedOptions.strokeAnimationSpeed = 500 / mergedOptions.strokeAnimationDuration;
  }
  if (options.strokeHighlightDuration && !options.strokeHighlightSpeed) {
    mergedOptions.strokeHighlightSpeed = 500 / mergedOptions.strokeHighlightDuration;
  }

  if (!options.highlightCompleteColor) {
    mergedOptions.highlightCompleteColor = mergedOptions.highlightColor;
  }

  return this._fillWidthAndHeight(mergedOptions);
};

// returns a new options object with width and height filled in if missing
HanziWriter.prototype._fillWidthAndHeight = function (options) {
  var filledOpts = assign({}, options);
  if (filledOpts.width && !filledOpts.height) {
    filledOpts.height = filledOpts.width;
  } else if (filledOpts.height && !filledOpts.width) {
    filledOpts.width = filledOpts.height;
  } else if (!filledOpts.width && !filledOpts.height) {
    var _target$getBoundingCl = this.target.getBoundingClientRect(),
        width = _target$getBoundingCl.width,
        height = _target$getBoundingCl.height;

    var minDim = Math.min(width, height);
    filledOpts.width = minDim;
    filledOpts.height = minDim;
  }
  return filledOpts;
};

HanziWriter.prototype._withData = function (func) {
  var _this14 = this;

  // if this._loadingManager.loadingFailed, then loading failed before this method was called
  if (this._loadingManager.loadingFailed) {
    throw Error('Failed to load character data. Call setCharacter and try again.');
  }
  return this._withDataPromise.then(function () {
    if (!_this14._loadingManager.loadingFailed) {
      return func();
    }
  });
};

HanziWriter.prototype._setupListeners = function () {
  var _this15 = this;

  this.target.addPointerStartListener(function (evt) {
    if (!_this15._quiz) return;
    evt.preventDefault();
    _this15._forwardToQuiz('startUserStroke', evt.getPoint());
  });
  this.target.addPointerMoveListener(function (evt) {
    if (!_this15._quiz) return;
    evt.preventDefault();
    _this15._forwardToQuiz('continueUserStroke', evt.getPoint());
  });
  this.target.addPointerEndListener(function () {
    return _this15._forwardToQuiz('endUserStroke');
  });
};

HanziWriter.prototype._forwardToQuiz = function (method) {
  var _quiz;

  if (!this._quiz) return;

  for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    args[_key - 1] = arguments[_key];
  }

  (_quiz = this._quiz)[method].apply(_quiz, args);
};

// --- Static Public API --- //

HanziWriter.create = function (element, character) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};

  var writer = new HanziWriter(element, options);
  writer.setCharacter(character);
  return writer;
};

var lastLoadingManager = null;
var lastLoadingOptions = null;

HanziWriter.loadCharacterData = function (character) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

  var loadingManager = void 0;
  if (lastLoadingManager && lastLoadingOptions === options) {
    loadingManager = lastLoadingManager;
  } else {
    loadingManager = new LoadingManager(assign({}, defaultOptions, options));
  }
  lastLoadingManager = loadingManager;
  lastLoadingOptions = options;
  return loadingManager.loadCharData(character);
};

HanziWriter.getScalingTransform = function (width, height) {
  var padding = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;

  var positioner = new Positioner({ width: width, height: height, padding: padding });
  return {
    x: positioner.xOffset,
    y: positioner.yOffset,
    scale: positioner.scale,
    transform: trim('\n      translate(' + positioner.xOffset + ', ' + (positioner.height - positioner.yOffset) + ')\n      scale(' + positioner.scale + ', ' + -1 * positioner.scale + ')\n    ').replace(/\s+/g, ' ')
  };
};

module.exports = HanziWriter;

/***/ }),
/* 10 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    copyAndMergeDeep = _require.copyAndMergeDeep,
    colorStringToVals = _require.colorStringToVals;

function RenderState(character, options, onStateChange) {
  this._onStateChange = onStateChange;
  this._mutationChains = [];
  this.state = {
    options: {
      drawingFadeDuration: options.drawingFadeDuration,
      drawingWidth: options.drawingWidth,
      drawingColor: colorStringToVals(options.drawingColor),
      strokeColor: colorStringToVals(options.strokeColor),
      outlineColor: colorStringToVals(options.outlineColor),
      radicalColor: colorStringToVals(options.radicalColor || options.strokeColor),
      highlightColor: colorStringToVals(options.highlightColor)
    },
    character: {
      main: {
        opacity: options.showCharacter ? 1 : 0,
        strokes: {}
      },
      outline: {
        opacity: options.showOutline ? 1 : 0,
        strokes: {}
      },
      highlight: {
        opacity: 1,
        strokes: {}
      }
    },
    userStrokes: null
  };
  for (var i = 0; i < character.strokes.length; i++) {
    this.state.character.main.strokes[i] = {
      opacity: 1,
      displayPortion: 1
    };
    this.state.character.outline.strokes[i] = {
      opacity: 1,
      displayPortion: 1
    };
    this.state.character.highlight.strokes[i] = {
      opacity: 0,
      displayPortion: 1
    };
  }
}

RenderState.prototype.updateState = function (stateChanges) {
  var nextState = copyAndMergeDeep(this.state, stateChanges);
  this._onStateChange(nextState, this.state);
  this.state = nextState;
};

RenderState.prototype.run = function (mutations) {
  var _this = this;

  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

  var scopes = mutations.map(function (mut) {
    return mut.scope;
  }).filter(function (x) {
    return x;
  });
  this.cancelMutations(scopes);
  return new Promise(function (resolve) {
    var mutationChain = {
      _isActive: true,
      _index: 0,
      _resolve: resolve,
      _mutations: mutations,
      _loop: options.loop,
      _scopes: scopes
    };
    _this._mutationChains.push(mutationChain);
    _this._run(mutationChain);
  });
};

RenderState.prototype._run = function (mutationChain) {
  var _this2 = this;

  if (!mutationChain._isActive) return;
  var mutations = mutationChain._mutations;
  if (mutationChain._index >= mutations.length) {
    if (mutationChain._loop) {
      mutationChain._index = 0; // eslint-disable-line no-param-reassign
    } else {
      mutationChain._isActive = false; // eslint-disable-line no-param-reassign
      this._mutationChains = this._mutationChains.filter(function (chain) {
        return chain !== mutationChain;
      });
      // The chain is done - resolve the promise to signal it finished successfully
      mutationChain._resolve({ canceled: false });
      return;
    }
  }

  var activeMutation = mutationChain._mutations[mutationChain._index];
  activeMutation.run(this).then(function () {
    if (mutationChain._isActive) {
      mutationChain._index++; // eslint-disable-line no-param-reassign
      _this2._run(mutationChain);
    }
  });
};

RenderState.prototype._getActiveMutations = function () {
  return this._mutationChains.map(function (chain) {
    return chain._mutations[chain._index];
  });
};

RenderState.prototype.pauseAll = function () {
  this._getActiveMutations().forEach(function (mutation) {
    return mutation.pause();
  });
};

RenderState.prototype.resumeAll = function () {
  this._getActiveMutations().forEach(function (mutation) {
    return mutation.resume();
  });
};

RenderState.prototype.cancelMutations = function (scopes) {
  var _this3 = this;

  this._mutationChains.forEach(function (chain) {
    chain._scopes.forEach(function (chainScope) {
      scopes.forEach(function (scope) {
        if (chainScope.indexOf(scope) >= 0 || scope.indexOf(chainScope) >= 0) {
          _this3._cancelMutationChain(chain);
        }
      });
    });
  });
};

RenderState.prototype.cancelAll = function () {
  this.cancelMutations(['']);
};

RenderState.prototype._cancelMutationChain = function (mutationChain) {
  mutationChain._isActive = false; // eslint-disable-line no-param-reassign
  for (var i = mutationChain._index; i < mutationChain._mutations.length; i++) {
    mutationChain._mutations[i].cancel(this);
  }
  if (mutationChain._resolve) {
    mutationChain._resolve({ canceled: true });
  }
  this._mutationChains = this._mutationChains.filter(function (chain) {
    return chain !== mutationChain;
  });
};

module.exports = RenderState;

/***/ }),
/* 11 */
/***/ (function(module, exports, __webpack_require__) {




var _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"]) _i["return"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError("Invalid attempt to destructure non-iterable instance"); } }; }();

var Stroke = __webpack_require__(12);
var Character = __webpack_require__(13);

var generateStrokes = function generateStrokes(charJson) {
  var isInRadical = function isInRadical(strokeNum) {
    return charJson.radStrokes && charJson.radStrokes.indexOf(strokeNum) >= 0;
  };

  return charJson.strokes.map(function (path, index) {
    var points = charJson.medians[index].map(function (pointData) {
      var _pointData = _slicedToArray(pointData, 2),
          x = _pointData[0],
          y = _pointData[1];

      return { x: x, y: y };
    });
    return new Stroke(path, points, index, isInRadical(index));
  });
};

var parseCharData = function parseCharData(symbol, charJson) {
  var strokes = generateStrokes(charJson);
  return new Character(symbol, strokes);
};

module.exports = parseCharData;

/***/ }),
/* 12 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(2),
    subtract = _require.subtract,
    distance = _require.distance,
    length = _require.length;

function Stroke(path, points, strokeNum) {
  var isInRadical = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;

  this.path = path;
  this.points = points;
  this.strokeNum = strokeNum;
  this.isInRadical = isInRadical;
}

Stroke.prototype.getStartingPoint = function () {
  return this.points[0];
};

Stroke.prototype.getEndingPoint = function () {
  return this.points[this.points.length - 1];
};

Stroke.prototype.getLength = function () {
  return length(this.points);
};

Stroke.prototype.getVectors = function () {
  var lastPoint = this.points[0];
  var pointsSansFirst = this.points.slice(1);
  return pointsSansFirst.map(function (point) {
    var vector = subtract(point, lastPoint);
    lastPoint = point;
    return vector;
  });
};

Stroke.prototype.getDistance = function (point) {
  var distances = this.points.map(function (strokePoint) {
    return distance(strokePoint, point);
  });
  return Math.min.apply(Math, distances);
};

Stroke.prototype.getAverageDistance = function (points) {
  var _this = this;

  var totalDist = points.reduce(function (acc, point) {
    return acc + _this.getDistance(point);
  }, 0);
  return totalDist / points.length;
};

module.exports = Stroke;

/***/ }),
/* 13 */
/***/ (function(module, exports, __webpack_require__) {




function Character(symbol, strokes) {
  this.symbol = symbol;
  this.strokes = strokes;
}

module.exports = Character;

/***/ }),
/* 14 */
/***/ (function(module, exports, __webpack_require__) {




// All makemeahanzi characters have the same bounding box
var CHARACTER_BOUNDS = [{ x: 0, y: -124 }, { x: 1024, y: 900 }];

function Positioner(options) {
  this._options = options;
  this.width = options.width;
  this.height = options.height;
  this._calculateScaleAndOffset();
}

Positioner.prototype.convertExternalPoint = function (point) {
  var x = (point.x - this.xOffset) / this.scale;
  var y = (this.height - this.yOffset - point.y) / this.scale;
  return { x: x, y: y };
};

Positioner.prototype._calculateScaleAndOffset = function () {
  var bounds = CHARACTER_BOUNDS;
  var preScaledWidth = bounds[1].x - bounds[0].x;
  var preScaledHeight = bounds[1].y - bounds[0].y;
  var effectiveWidth = this.width - 2 * this._options.padding;
  var effectiveHeight = this.height - 2 * this._options.padding;
  var scaleX = effectiveWidth / preScaledWidth;
  var scaleY = effectiveHeight / preScaledHeight;

  this.scale = Math.min(scaleX, scaleY);

  var xCenteringBuffer = this._options.padding + (effectiveWidth - this.scale * preScaledWidth) / 2;
  var yCenteringBuffer = this._options.padding + (effectiveHeight - this.scale * preScaledHeight) / 2;

  this.xOffset = -1 * bounds[0].x * this.scale + xCenteringBuffer;
  this.yOffset = -1 * bounds[0].y * this.scale + yCenteringBuffer;
};

module.exports = Positioner;

/***/ }),
/* 15 */
/***/ (function(module, exports, __webpack_require__) {




var strokeMatches = __webpack_require__(16);
var UserStroke = __webpack_require__(17);

var _require = __webpack_require__(0),
    callIfExists = _require.callIfExists,
    counter = _require.counter;

var quizActions = __webpack_require__(18);
var geometry = __webpack_require__(2);
var characterActions = __webpack_require__(4);

var getDrawnPath = function getDrawnPath(userStroke) {
  return {
    pathString: geometry.getPathString(userStroke.externalPoints),
    points: userStroke.points.map(function (point) {
      return geometry.round(point);
    })
  };
};

function Quiz(character, renderState, positioner) {
  this._character = character;
  this._renderState = renderState;
  this._isActive = false;
  this._positioner = positioner;
}

Quiz.prototype.startQuiz = function (options) {
  this._isActive = true;
  this._options = options;
  this._currentStrokeIndex = 0;
  this._numRecentMistakes = 0;
  this._totalMistakes = 0;
  this._renderState.run(quizActions.startQuiz(this._character, options.strokeFadeDuration));
};

Quiz.prototype.startUserStroke = function (externalPoint) {
  var point = this._positioner.convertExternalPoint(externalPoint);
  if (!this._isActive) return null;
  if (this._userStroke) return this.endUserStroke();
  var strokeId = counter();
  this._userStroke = new UserStroke(strokeId, point, externalPoint);
  this._renderState.run(quizActions.startUserStroke(strokeId, point));
};

Quiz.prototype.continueUserStroke = function (externalPoint) {
  if (!this._userStroke) return;
  var point = this._positioner.convertExternalPoint(externalPoint);
  this._userStroke.appendPoint(point, externalPoint);
  var nextPoints = this._userStroke.points.slice(0);
  this._renderState.run(quizActions.updateUserStroke(this._userStroke.id, nextPoints));
};

Quiz.prototype.endUserStroke = function () {
  if (!this._userStroke) return;

  this._renderState.run(quizActions.removeUserStroke(this._userStroke.id, this._options.drawingFadeDuration));
  // skip single-point strokes
  if (this._userStroke.points.length === 1) {
    this._userStroke = null;
    return;
  }

  var currentStroke = this._getCurrentStroke();
  var isOutlineVisible = this._renderState.state.character.outline.opacity > 0;
  var isMatch = strokeMatches(this._userStroke, this._character, this._currentStrokeIndex, {
    isOutlineVisible: isOutlineVisible,
    leniency: this._options.leniency
  });

  if (isMatch) {
    this._handleSuccess();
  } else {
    this._handleFailure();
    if (this._numRecentMistakes >= this._options.showHintAfterMisses && this._options.showHintAfterMisses !== false) {
      this._renderState.run(quizActions.highlightStroke(currentStroke, this._options.highlightColor, this._options.strokeHighlightSpeed));
    }
  }
  this._userStroke = null;
};

Quiz.prototype.cancel = function () {
  this._isActive = false;
  if (this._userStroke) {
    this._renderState.run(quizActions.removeUserStroke(this._userStroke.id, this._options.drawingFadeDuration));
  }
};

Quiz.prototype._handleSuccess = function () {
  callIfExists(this._options.onCorrectStroke, {
    character: this._character.symbol,
    strokeNum: this._currentStrokeIndex,
    mistakesOnStroke: this._numRecentMistakes,
    totalMistakes: this._totalMistakes,
    strokesRemaining: this._character.strokes.length - this._currentStrokeIndex - 1,
    drawnPath: getDrawnPath(this._userStroke)
  });
  var animation = characterActions.showStroke('main', this._currentStrokeIndex, this._options.strokeFadeDuration);
  this._currentStrokeIndex += 1;
  this._numRecentMistakes = 0;

  if (this._currentStrokeIndex === this._character.strokes.length) {
    this._isActive = false;
    callIfExists(this._options.onComplete, {
      character: this._character.symbol,
      totalMistakes: this._totalMistakes
    });
    if (this._options.highlightOnComplete) {
      animation = animation.concat(quizActions.highlightCompleteChar(this._character, this._options.highlightCompleteColor, this._options.strokeHighlightDuration * 2));
    }
  }
  this._renderState.run(animation);
};

Quiz.prototype._handleFailure = function () {
  this._numRecentMistakes += 1;
  this._totalMistakes += 1;
  callIfExists(this._options.onMistake, {
    character: this._character.symbol,
    strokeNum: this._currentStrokeIndex,
    mistakesOnStroke: this._numRecentMistakes,
    totalMistakes: this._totalMistakes,
    strokesRemaining: this._character.strokes.length - this._currentStrokeIndex,
    drawnPath: getDrawnPath(this._userStroke)
  });
};

Quiz.prototype._getCurrentStroke = function () {
  return this._character.strokes[this._currentStrokeIndex];
};

module.exports = Quiz;

/***/ }),
/* 16 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    average = _require.average,
    assign = _require.assign;

var _require2 = __webpack_require__(2),
    cosineSimilarity = _require2.cosineSimilarity,
    equals = _require2.equals,
    frechetDist = _require2.frechetDist,
    distance = _require2.distance,
    subtract = _require2.subtract,
    normalizeCurve = _require2.normalizeCurve,
    rotate = _require2.rotate,
    length = _require2.length;

var AVG_DIST_THRESHOLD = 350; // bigger = more lenient
var COSINE_SIMILARITY_THRESHOLD = 0; // -1 to 1, smaller = more lenient
var START_AND_END_DIST_THRESHOLD = 250; // bigger = more lenient
var FRECHET_THRESHOLD = 0.40; // bigger = more lenient
var MIN_LEN_THRESHOLD = 0.35; // smaller = more lenient

var startAndEndMatches = function startAndEndMatches(points, closestStroke, leniency) {
  var startingDist = distance(closestStroke.getStartingPoint(), points[0]);
  var endingDist = distance(closestStroke.getEndingPoint(), points[points.length - 1]);
  return startingDist <= START_AND_END_DIST_THRESHOLD * leniency && endingDist <= START_AND_END_DIST_THRESHOLD * leniency;
};

// returns a list of the direction of all segments in the line connecting the points
var getEdgeVectors = function getEdgeVectors(points) {
  var vectors = [];
  var lastPoint = points[0];
  points.slice(1).forEach(function (point) {
    vectors.push(subtract(point, lastPoint));
    lastPoint = point;
  });
  return vectors;
};

var directionMatches = function directionMatches(points, stroke) {
  var edgeVectors = getEdgeVectors(points);
  var strokeVectors = stroke.getVectors();
  var similarities = edgeVectors.map(function (edgeVector) {
    var strokeSimilarities = strokeVectors.map(function (strokeVector) {
      return cosineSimilarity(strokeVector, edgeVector);
    });
    return Math.max.apply(Math, strokeSimilarities);
  });
  var avgSimilarity = average(similarities);
  return avgSimilarity > COSINE_SIMILARITY_THRESHOLD;
};

var lengthMatches = function lengthMatches(points, stroke, leniency) {
  return leniency * (length(points) + 25) / (stroke.getLength() + 25) >= MIN_LEN_THRESHOLD;
};

var stripDuplicates = function stripDuplicates(points) {
  if (points.length < 2) return points;
  var dedupedPoints = [points[0]];
  points.slice(1).forEach(function (point) {
    if (!equals(point, dedupedPoints[dedupedPoints.length - 1])) {
      dedupedPoints.push(point);
    }
  });
  return dedupedPoints;
};

var SHAPE_FIT_ROTATIONS = [Math.PI / 16, Math.PI / 32, 0, -1 * Math.PI / 32, -1 * Math.PI / 16];

var shapeFit = function shapeFit(curve1, curve2, leniency) {
  var normCurve1 = normalizeCurve(curve1);
  var normCurve2 = normalizeCurve(curve2);
  var minDist = Infinity;
  SHAPE_FIT_ROTATIONS.forEach(function (theta) {
    var dist = frechetDist(normCurve1, rotate(normCurve2, theta));
    if (dist < minDist) {
      minDist = dist;
    }
  });
  return minDist <= FRECHET_THRESHOLD * leniency;
};

var getMatchData = function getMatchData(points, stroke, options) {
  var _options$leniency = options.leniency,
      leniency = _options$leniency === undefined ? 1 : _options$leniency,
      _options$isOutlineVis = options.isOutlineVisible,
      isOutlineVisible = _options$isOutlineVis === undefined ? false : _options$isOutlineVis;

  var avgDist = stroke.getAverageDistance(points);
  var distMod = isOutlineVisible || stroke.strokeNum > 0 ? 0.5 : 1;
  var withinDistThresh = avgDist <= AVG_DIST_THRESHOLD * distMod * leniency;
  // short circuit for faster matching
  if (!withinDistThresh) {
    return { isMatch: false, avgDist: avgDist };
  }
  var startAndEndMatch = startAndEndMatches(points, stroke, leniency);
  var directionMatch = directionMatches(points, stroke);
  var shapeMatch = shapeFit(points, stroke.points, leniency);
  var lengthMatch = lengthMatches(points, stroke, leniency);
  return {
    isMatch: withinDistThresh && startAndEndMatch && directionMatch && shapeMatch && lengthMatch,
    avgDist: avgDist
  };
};

var strokeMatches = function strokeMatches(userStroke, character, strokeNum) {
  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};

  var points = stripDuplicates(userStroke.points);
  if (points.length < 2) return null;

  var strokeMatchData = getMatchData(points, character.strokes[strokeNum], options);
  if (!strokeMatchData.isMatch) return false;

  // if there is a better match among strokes the user hasn't drawn yet, the user probably drew the wrong stroke
  var laterStrokes = character.strokes.slice(strokeNum + 1);
  var closestMatchDist = strokeMatchData.avgDist;
  for (var i = 0; i < laterStrokes.length; i++) {
    var laterMatchData = getMatchData(points, laterStrokes[i], options);
    if (laterMatchData.isMatch && laterMatchData.avgDist < closestMatchDist) {
      closestMatchDist = laterMatchData.avgDist;
    }
  }
  // if there's a better match, rather that returning false automatically, try reducing leniency instead
  // if leniency is already really high we can allow some similar strokes to pass
  if (closestMatchDist < strokeMatchData.avgDist) {
    // adjust leniency between 0.3 and 0.6 depending on how much of a better match the new match is
    var leniencyAdjustment = 0.6 * (closestMatchDist + strokeMatchData.avgDist) / (2 * strokeMatchData.avgDist);
    var newLeniency = (options.leniency || 1) * leniencyAdjustment;
    var adjustedOptions = assign({}, options, { leniency: newLeniency });
    var adjustedStrokeMatchData = getMatchData(points, character.strokes[strokeNum], adjustedOptions);
    return adjustedStrokeMatchData.isMatch;
  }
  return true;
};

module.exports = strokeMatches;

/***/ }),
/* 17 */
/***/ (function(module, exports, __webpack_require__) {




function UserStroke(id, startingPoint, startingExternalPoint) {
  this.id = id;
  this.points = [startingPoint];
  this.externalPoints = [startingExternalPoint];
}

UserStroke.prototype.appendPoint = function (point, externalPoint) {
  this.points.push(point);
  this.externalPoints.push(externalPoint);
};

module.exports = UserStroke;

/***/ }),
/* 18 */
/***/ (function(module, exports, __webpack_require__) {




var Mutation = __webpack_require__(5);
var characterActions = __webpack_require__(4);

var _require = __webpack_require__(0),
    objRepeat = _require.objRepeat;

var startQuiz = function startQuiz(character, fadeDuration) {
  return characterActions.hideCharacter('main', character, fadeDuration).concat([new Mutation('character.highlight', {
    opacity: 1,
    strokes: objRepeat({ opacity: 0 }, character.strokes.length)
  }, { force: true }), new Mutation('character.main', {
    opacity: 1,
    strokes: objRepeat({ opacity: 0 }, character.strokes.length)
  }, { force: true })]);
};

var startUserStroke = function startUserStroke(id, point) {
  return [new Mutation('quiz.activeUserStrokeId', id, { force: true }), new Mutation('userStrokes.' + id, {
    points: [point],
    opacity: 1
  }, { force: true })];
};

var updateUserStroke = function updateUserStroke(userStrokeId, points) {
  return [new Mutation('userStrokes.' + userStrokeId + '.points', points, { force: true })];
};

var removeUserStroke = function removeUserStroke(userStrokeId, duration) {
  return [new Mutation('userStrokes.' + userStrokeId + '.opacity', 0, { duration: duration }), new Mutation('userStrokes.' + userStrokeId, null, { force: true })];
};

var highlightCompleteChar = function highlightCompleteChar(character, color, duration) {
  return [new Mutation('character.highlight.strokeColor', color)].concat(characterActions.hideCharacter('highlight', character)).concat(characterActions.showCharacter('highlight', character, duration / 2)).concat(characterActions.hideCharacter('highlight', character, duration / 2));
};

module.exports = {
  highlightCompleteChar: highlightCompleteChar,
  highlightStroke: characterActions.highlightStroke,
  startQuiz: startQuiz,
  startUserStroke: startUserStroke,
  updateUserStroke: updateUserStroke,
  removeUserStroke: removeUserStroke
};

/***/ }),
/* 19 */
/***/ (function(module, exports, __webpack_require__) {




var HanziWriterRenderer = __webpack_require__(20);
var RenderTarget = __webpack_require__(24);

module.exports = {
  HanziWriterRenderer: HanziWriterRenderer,
  createRenderTarget: RenderTarget.init
};

/***/ }),
/* 20 */
/***/ (function(module, exports, __webpack_require__) {




var CharacterRenderer = __webpack_require__(21);
var UserStrokeRenderer = __webpack_require__(23);

var _require = __webpack_require__(0),
    assign = _require.assign;

var svg = __webpack_require__(3);

function HanziWriterRenderer(character, positioner) {
  this._character = character;
  this._positioner = positioner;
  this._mainCharRenderer = new CharacterRenderer(character);
  this._outlineCharRenderer = new CharacterRenderer(character);
  this._highlightCharRenderer = new CharacterRenderer(character);
  this._userStrokeRenderers = {};
}

HanziWriterRenderer.prototype.mount = function (target) {
  var positionedTarget = target.createSubRenderTarget();
  var group = positionedTarget.svg;
  svg.attr(group, 'transform', '\n    translate(' + this._positioner.xOffset + ', ' + (this._positioner.height - this._positioner.yOffset) + ')\n    scale(' + this._positioner.scale + ', ' + -1 * this._positioner.scale + ')\n  ');
  this._outlineCharRenderer.mount(positionedTarget);
  this._mainCharRenderer.mount(positionedTarget);
  this._highlightCharRenderer.mount(positionedTarget);
  this._positionedTarget = positionedTarget;
};

HanziWriterRenderer.prototype.render = function (props) {
  var _this = this;

  this._outlineCharRenderer.render({
    opacity: props.character.outline.opacity,
    strokes: props.character.outline.strokes,
    strokeColor: props.options.outlineColor
  });
  this._mainCharRenderer.render({
    opacity: props.character.main.opacity,
    strokes: props.character.main.strokes,
    strokeColor: props.options.strokeColor,
    radicalColor: props.options.radicalColor
  });
  this._highlightCharRenderer.render({
    opacity: props.character.highlight.opacity,
    strokes: props.character.highlight.strokes,
    strokeColor: props.options.highlightColor
  });

  var userStrokes = props.userStrokes || {};
  Object.keys(this._userStrokeRenderers).forEach(function (userStrokeId) {
    if (!userStrokes[userStrokeId]) {
      _this._userStrokeRenderers[userStrokeId].destroy();
      delete _this._userStrokeRenderers[userStrokeId];
    }
  });

  Object.keys(userStrokes).forEach(function (userStrokeId) {
    if (!userStrokes[userStrokeId]) return;
    var userStrokeProps = assign({
      strokeWidth: props.options.drawingWidth,
      strokeColor: props.options.drawingColor
    }, userStrokes[userStrokeId]);
    var strokeRenderer = _this._userStrokeRenderers[userStrokeId];
    if (!strokeRenderer) {
      strokeRenderer = new UserStrokeRenderer();
      strokeRenderer.mount(_this._positionedTarget);
      _this._userStrokeRenderers[userStrokeId] = strokeRenderer;
    }
    strokeRenderer.render(userStrokeProps);
  });
};

HanziWriterRenderer.prototype.destroy = function () {
  svg.removeElm(this._positionedTarget.svg);
  this._positionedTarget.defs.innerHTML = '';
};

module.exports = HanziWriterRenderer;

/***/ }),
/* 21 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    isMsBrowser = _require.isMsBrowser;

var StrokeRenderer = __webpack_require__(22);

function CharacterRenderer(character) {
  this._oldProps = {};
  this._strokeRenderers = character.strokes.map(function (stroke) {
    return new StrokeRenderer(stroke);
  });
}

CharacterRenderer.prototype.mount = function (target) {
  var subTarget = target.createSubRenderTarget();
  this._group = subTarget.svg;
  this._strokeRenderers.forEach(function (strokeRenderer, i) {
    strokeRenderer.mount(subTarget);
  });
};

CharacterRenderer.prototype.render = function (props) {
  if (props === this._oldProps) return;
  if (props.opacity !== this._oldProps.opacity) {
    this._group.style.opacity = props.opacity;
    // MS browsers seem to have a bug where if SVG is set to display:none, it sometimes breaks.
    // More info: https://github.com/chanind/hanzi-writer/issues/164
    // this is just a perf improvement, so disable for MS browsers
    if (!isMsBrowser) {
      if (props.opacity === 0) {
        this._group.style.display = 'none';
      } else if (this._oldProps.opacity === 0) {
        this._group.style.removeProperty('display');
      }
    }
  }
  var colorsChanged = !this._oldProps || props.strokeColor !== this._oldProps.strokeColor || props.radicalColor !== this._oldProps.radicalColor;
  if (colorsChanged || props.strokes !== this._oldProps.strokes) {
    for (var i = 0; i < this._strokeRenderers.length; i++) {
      if (!colorsChanged && this._oldProps.strokes && props.strokes[i] === this._oldProps.strokes[i]) continue;
      this._strokeRenderers[i].render({
        strokeColor: props.strokeColor,
        radicalColor: props.radicalColor,
        opacity: props.strokes[i].opacity,
        displayPortion: props.strokes[i].displayPortion
      });
    }
  }
  this._oldProps = props;
};

module.exports = CharacterRenderer;

/***/ }),
/* 22 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    counter = _require.counter;

var svg = __webpack_require__(3);

var _require2 = __webpack_require__(2),
    extendStart = _require2.extendStart,
    getPathString = _require2.getPathString;

var StrokeRendererBase = __webpack_require__(6);

var STROKE_WIDTH = 200;

// this is a stroke composed of several stroke parts
function StrokeRenderer(stroke) {
  this._oldProps = {};
  this._stroke = stroke;
  this._pathLength = stroke.getLength() + STROKE_WIDTH / 2;
}
StrokeRenderer.prototype = Object.create(StrokeRendererBase.prototype);

StrokeRenderer.prototype.mount = function (target) {
  this._animationPath = svg.createElm('path');
  this._clip = svg.createElm('clipPath');
  this._strokePath = svg.createElm('path');
  var maskId = 'mask-' + counter();
  svg.attr(this._clip, 'id', maskId);

  svg.attr(this._strokePath, 'd', this._stroke.path);
  this._animationPath.style.opacity = 0;
  svg.attr(this._animationPath, 'clip-path', svg.urlIdRef(maskId));

  var extendedMaskPoints = extendStart(this._stroke.points, STROKE_WIDTH / 2);
  svg.attr(this._animationPath, 'd', getPathString(extendedMaskPoints));
  svg.attrs(this._animationPath, {
    stroke: '#FFFFFF',
    'stroke-width': STROKE_WIDTH,
    fill: 'none',
    'stroke-linecap': 'round',
    'stroke-linejoin': 'miter',
    'stroke-dasharray': this._pathLength + ',' + this._pathLength
  });

  this._clip.appendChild(this._strokePath);
  target.defs.appendChild(this._clip);
  target.svg.appendChild(this._animationPath);
  return this;
};

StrokeRenderer.prototype.render = function (props) {
  if (props === this._oldProps) return;
  if (props.displayPortion !== this._oldProps.displayPortion) {
    this._animationPath.style.strokeDashoffset = this._getStrokeDashoffset(props.displayPortion);
  }

  var color = this._getColor(props);
  if (color !== this._getColor(this._oldProps)) {
    var r = color.r,
        g = color.g,
        b = color.b,
        a = color.a;

    svg.attrs(this._animationPath, { stroke: 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')' });
  }

  if (props.opacity !== this._oldProps.opacity) {
    this._animationPath.style.opacity = props.opacity;
  }
  this._oldProps = props;
};

module.exports = StrokeRenderer;

/***/ }),
/* 23 */
/***/ (function(module, exports, __webpack_require__) {




var svg = __webpack_require__(3);

var _require = __webpack_require__(2),
    getPathString = _require.getPathString;

function UserStrokeRenderer() {
  this._oldProps = {};
}

UserStrokeRenderer.prototype.mount = function (target) {
  this._path = svg.createElm('path');
  target.svg.appendChild(this._path);
};

UserStrokeRenderer.prototype.render = function (props) {
  if (props === this._oldProps) return;
  if (props.strokeColor !== this._oldProps.strokeColor || props.strokeWidth !== this._oldProps.strokeWidth) {
    var _props$strokeColor = props.strokeColor,
        r = _props$strokeColor.r,
        g = _props$strokeColor.g,
        b = _props$strokeColor.b,
        a = _props$strokeColor.a;

    svg.attrs(this._path, {
      fill: 'none',
      stroke: 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')',
      'stroke-width': props.strokeWidth,
      'stroke-linecap': 'round',
      'stroke-linejoin': 'round'
    });
  }
  if (props.opacity !== this._oldProps.opacity) {
    svg.attr(this._path, 'opacity', props.opacity);
  }
  if (props.points !== this._oldProps.points) {
    svg.attr(this._path, 'd', getPathString(props.points));
  }
  this._oldProps = props;
};

UserStrokeRenderer.prototype.destroy = function () {
  svg.removeElm(this._path);
};

module.exports = UserStrokeRenderer;

/***/ }),
/* 24 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

var _require = __webpack_require__(3),
    createElm = _require.createElm,
    attrs = _require.attrs;

var RenderTargetBase = __webpack_require__(7);

function RenderTarget(svg, defs) {
  this.svg = svg;
  this.defs = defs;
  this.node = svg;

  if (this.node.createSVGPoint) {
    this._pt = this.node.createSVGPoint();
  }
}
RenderTarget.prototype = Object.create(RenderTargetBase.prototype);

RenderTarget.prototype.createSubRenderTarget = function () {
  var group = createElm('g');
  this.svg.appendChild(group);
  return new RenderTarget(group, this.defs);
};

RenderTarget.prototype._getMousePoint = function (evt) {
  if (this._pt) {
    this._pt.x = evt.clientX;
    this._pt.y = evt.clientY;
    var localPt = this._pt.matrixTransform(this.node.getScreenCTM().inverse());
    return { x: localPt.x, y: localPt.y };
  }
  return RenderTargetBase.prototype._getMousePoint.call(this, evt);
};

RenderTarget.prototype._getTouchPoint = function (evt) {
  if (this._pt) {
    this._pt.x = evt.touches[0].clientX;
    this._pt.y = evt.touches[0].clientY;
    var localPt = this._pt.matrixTransform(this.node.getScreenCTM().inverse());
    return { x: localPt.x, y: localPt.y };
  }
  return RenderTargetBase.prototype._getTouchPoint.call(this, evt);
};

RenderTarget.init = function (elmOrId) {
  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '100%';
  var height = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '100%';

  var svg = void 0;
  var elm = elmOrId;
  if (typeof elmOrId === 'string') {
    elm = global.document.getElementById(elmOrId);
  }
  if (!elm) {
    throw new Error('HanziWriter target element not found: ' + elmOrId);
  }
  var nodeType = elm.nodeName.toUpperCase();
  if (nodeType === 'SVG' || nodeType === 'G') {
    svg = elm;
  } else {
    svg = createElm('svg');
    elm.appendChild(svg);
  }
  attrs(svg, { width: width, height: height });
  var defs = createElm('defs');
  svg.appendChild(defs);
  return new RenderTarget(svg, defs);
};

module.exports = RenderTarget;
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 25 */
/***/ (function(module, exports, __webpack_require__) {




var HanziWriterRenderer = __webpack_require__(26);
var RenderTarget = __webpack_require__(30);

module.exports = {
  HanziWriterRenderer: HanziWriterRenderer,
  createRenderTarget: RenderTarget.init
};

/***/ }),
/* 26 */
/***/ (function(module, exports, __webpack_require__) {




var CharacterRenderer = __webpack_require__(27);
var renderUserStroke = __webpack_require__(29);

var _require = __webpack_require__(0),
    assign = _require.assign;

function HanziWriterRenderer(character, positioner) {
  this._character = character;
  this._positioner = positioner;
  this._mainCharRenderer = new CharacterRenderer(character);
  this._outlineCharRenderer = new CharacterRenderer(character);
  this._highlightCharRenderer = new CharacterRenderer(character);
}

HanziWriterRenderer.prototype.mount = function (target) {
  this._target = target;
};

HanziWriterRenderer.prototype._animationFrame = function (func) {
  var ctx = this._target.getContext();
  ctx.clearRect(0, 0, this._positioner.width, this._positioner.height);

  ctx.save();
  ctx.translate(this._positioner.xOffset, this._positioner.height - this._positioner.yOffset);
  ctx.transform(1, 0, 0, -1, 0, 0);
  ctx.scale(this._positioner.scale, this._positioner.scale);
  func(ctx);
  ctx.restore();
  if (ctx.draw) ctx.draw();
};

HanziWriterRenderer.prototype.render = function (props) {
  var _this = this;

  this._animationFrame(function (ctx) {
    _this._outlineCharRenderer.render(ctx, {
      opacity: props.character.outline.opacity,
      strokes: props.character.outline.strokes,
      strokeColor: props.options.outlineColor
    });
    _this._mainCharRenderer.render(ctx, {
      opacity: props.character.main.opacity,
      strokes: props.character.main.strokes,
      strokeColor: props.options.strokeColor,
      radicalColor: props.options.radicalColor
    });
    _this._highlightCharRenderer.render(ctx, {
      opacity: props.character.highlight.opacity,
      strokes: props.character.highlight.strokes,
      strokeColor: props.options.highlightColor
    });

    var userStrokes = props.userStrokes || {};
    Object.keys(userStrokes).forEach(function (userStrokeId) {
      if (userStrokes[userStrokeId]) {
        var userStrokeProps = assign({
          strokeWidth: props.options.drawingWidth,
          strokeColor: props.options.drawingColor
        }, userStrokes[userStrokeId]);
        renderUserStroke(ctx, userStrokeProps);
      }
    });
  });
};

HanziWriterRenderer.prototype.destroy = function () {};

module.exports = HanziWriterRenderer;

/***/ }),
/* 27 */
/***/ (function(module, exports, __webpack_require__) {




var StrokeRenderer = __webpack_require__(28);

function CharacterRenderer(character) {
  this._strokeRenderers = character.strokes.map(function (stroke) {
    return new StrokeRenderer(stroke);
  });
}

CharacterRenderer.prototype.render = function (ctx, props) {
  if (props.opacity < 0.05) return;
  for (var i = 0; i < this._strokeRenderers.length; i++) {
    this._strokeRenderers[i].render(ctx, {
      strokeColor: props.strokeColor,
      radicalColor: props.radicalColor,
      opacity: props.strokes[i].opacity * props.opacity,
      displayPortion: props.strokes[i].displayPortion
    });
  }
};

module.exports = CharacterRenderer;

/***/ }),
/* 28 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

var _require = __webpack_require__(2),
    extendStart = _require.extendStart;

var _require2 = __webpack_require__(8),
    drawPath = _require2.drawPath,
    pathStringToCanvas = _require2.pathStringToCanvas;

var StrokeRendererBase = __webpack_require__(6);

var STROKE_WIDTH = 200;

// this is a stroke composed of several stroke parts
function StrokeRenderer(stroke) {
  var usePath2D = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;

  this._stroke = stroke;
  this._pathLength = stroke.getLength() + STROKE_WIDTH / 2;
  if (usePath2D && (global.Path2D || (typeof wx !== 'undefined' && wx.createOffscreenCanvas))) {
    // 微信小程序环境下禁用 Path2D，因为不支持 new Path2D()
    if (typeof wx !== 'undefined') {
      this._pathCmd = pathStringToCanvas(this._stroke.path);
    } else if (global.Path2D) {
      this._path2D = new global.Path2D(this._stroke.path);
    } else {
      this._pathCmd = pathStringToCanvas(this._stroke.path);
    }
  } else {
    this._pathCmd = pathStringToCanvas(this._stroke.path);
  }
  this._extendedMaskPoints = extendStart(this._stroke.points, STROKE_WIDTH / 2);
}
StrokeRenderer.prototype = Object.create(StrokeRendererBase.prototype);

StrokeRenderer.prototype.render = function (ctx, props) {
  if (props.opacity < 0.05) return;

  ctx.save();
  if (this._path2D) {
    ctx.clip(this._path2D);
  } else {
    this._pathCmd(ctx);
    // wechat bugs out if the clip path isn't stroked or filled
    ctx.globalAlpha = 0;
    ctx.stroke();
    ctx.clip();
  }

  var _getColor = this._getColor(props),
      r = _getColor.r,
      g = _getColor.g,
      b = _getColor.b,
      a = _getColor.a;

  var color = a === 1 ? 'rgb(' + r + ',' + g + ',' + b + ')' : 'rgb(' + r + ',' + g + ',' + b + ',' + a + ')';
  var dashOffset = this._getStrokeDashoffset(props.displayPortion);
  ctx.globalAlpha = props.opacity;
  ctx.strokeStyle = color;
  ctx.fillStyle = color;
  ctx.lineWidth = STROKE_WIDTH;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  // wechat sets dashOffset as a second param here. Should be harmless for browsers to add here too
  ctx.setLineDash([this._pathLength, this._pathLength], dashOffset);
  ctx.lineDashOffset = dashOffset;
  drawPath(ctx, this._extendedMaskPoints);

  ctx.restore();
};

module.exports = StrokeRenderer;
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 29 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(8),
    drawPath = _require.drawPath;

module.exports = function (ctx, props) {
  if (props.opacity < 0.05) return;
  var _props$strokeColor = props.strokeColor,
      r = _props$strokeColor.r,
      g = _props$strokeColor.g,
      b = _props$strokeColor.b,
      a = _props$strokeColor.a;


  ctx.save();
  ctx.globalAlpha = props.opacity;
  ctx.lineWidth = props.strokeWidth;
  ctx.strokeStyle = 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')';
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  drawPath(ctx, props.points);
  ctx.restore();
};

/***/ }),
/* 30 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

var RenderTargetBase = __webpack_require__(7);

function RenderTarget(canvas) {
  this.node = canvas;
}
RenderTarget.prototype = Object.create(RenderTargetBase.prototype);

RenderTarget.prototype.getContext = function () {
  return this.node.getContext('2d');
};

RenderTarget.init = function (elmOrId) {
  var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '100%';
  var height = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '100%';

  var canvas = void 0;
  var elm = elmOrId;
  if (typeof elmOrId === 'string') {
    elm = global.document.getElementById(elmOrId);
  }
  if (!elm) {
    throw new Error('HanziWriter target element not found: ' + elmOrId);
  }
  var nodeType = elm.nodeName.toUpperCase();
  if (nodeType === 'CANVAS') {
    canvas = elm;
  } else {
    canvas = global.document.createElement('canvas');
    elm.appendChild(canvas);
  }
  canvas.setAttribute('width', width);
  canvas.setAttribute('height', height);
  return new RenderTarget(canvas);
};

module.exports = RenderTarget;
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 31 */
/***/ (function(module, exports, __webpack_require__) {


/* WEBPACK VAR INJECTION */(function(global) {

var VERSION = '2.0';
var getCharDataUrl = function getCharDataUrl(char) {
  return 'https://cdn.jsdelivr.net/npm/hanzi-writer-data@' + VERSION + '/' + char + '.json';
};

module.exports = function (char, onLoad, onError) {
  // load char data from hanziwriter cdn (currently hosted on jsdelivr)
  var xhr = new global.XMLHttpRequest();
  if (xhr.overrideMimeType) {
    // IE 9 and 10 don't seem to support this...
    xhr.overrideMimeType('application/json');
  }
  xhr.open('GET', getCharDataUrl(char), true);
  xhr.onerror = function (event) {
    onError(xhr, event);
  };
  xhr.onreadystatechange = function () {
    // TODO: error handling
    if (xhr.readyState !== 4) return;

    if (xhr.status === 200) {
      onLoad(JSON.parse(xhr.responseText));
    } else if (xhr.status !== 0 && onError) {
      onError(xhr);
    }
  };
  xhr.send(null);
};
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(1)))

/***/ }),
/* 32 */
/***/ (function(module, exports, __webpack_require__) {




var _require = __webpack_require__(0),
    callIfExists = _require.callIfExists;

function LoadingManager(options) {
  this._loadCounter = 0;
  this._options = options;
  this._isLoading = false;

  // use this to attribute to determine if there was a problem with loading
  this.loadingFailed = false;
}

LoadingManager.prototype._debouncedLoad = function (char, count) {
  var _this = this;

  // these wrappers ignore all responses except the most recent.
  var wrappedResolve = function wrappedResolve(data) {
    if (count === _this._loadCounter) _this._resolve(data);
  };
  var wrappedReject = function wrappedReject(reason) {
    if (count === _this._loadCounter) _this._reject(reason);
  };

  var returnedData = this._options.charDataLoader(char, wrappedResolve, wrappedReject);
  if (returnedData) wrappedResolve(returnedData);
};

LoadingManager.prototype._setupLoadingPromise = function () {
  var _this2 = this;

  return new Promise(function (resolve, reject) {
    _this2._resolve = resolve;
    _this2._reject = reject;
  }).then(function (data) {
    _this2._isLoading = false;
    callIfExists(_this2._options.onLoadCharDataSuccess, data);
    return data;
  }, function (reason) {
    _this2._isLoading = false;
    _this2.loadingFailed = true;
    callIfExists(_this2._options.onLoadCharDataError, reason);
    // If error callback wasn't provided, throw an error so the developer will be aware something went wrong
    if (!_this2._options.onLoadCharDataError) {
      if (reason instanceof Error) throw reason;
      var err = new Error('Failed to load char data for ' + _this2._loadingChar);
      err.reason = reason;
      throw err;
    }
  });
};

LoadingManager.prototype.loadCharData = function (char) {
  this._loadingChar = char;
  var promise = this._setupLoadingPromise();
  this.loadingFailed = false;
  this._isLoading = true;
  this._loadCounter++;
  this._debouncedLoad(char, this._loadCounter);
  return promise;
};

module.exports = LoadingManager;

/***/ })
/******/ ]);
});
}, function(modId) {var map = {}; return __REQUIRE__(map[modId], modId); })
return __REQUIRE__(1748932288066);
})()
//miniprogram-npm-outsideDeps=[]
//# sourceMappingURL=index.js.map
# 字体加载错误修复说明

## 问题描述
```
[渲染层网络层错误] Failed to load font http://at.alicdn.com/t/c/font_2553510_kfwma2yq1rs.woff2?t=1694918397022
net::ERR_CACHE_MISS
```

## 问题原因
Vant Weapp 组件库中的 `van-icon` 组件依赖外部字体资源（阿里云图标库），但微信小程序对外部资源访问有限制，导致字体加载失败。

## 修复方案
采用完全移除 `van-icon` 组件的策略，使用文本符号替代：

### 1. 错题集页面 (wrongbook)
- **文件**: `miniprogram/pages/wrongbook/wrongbook.wxml`
- **修改**: 
  - `<van-icon name="success" />` → `<view class="success-icon">✓</view>`
  - `<van-icon name="cross" />` → `<view class="close-btn">✕</view>`
- **样式**: 添加 `.success-icon` 和 `.close-btn` 样式

### 2. 首页 (home)
- **文件**: `miniprogram/pages/home/<USER>
- **修改**: 
  - `<van-icon name="cross" />` → `<text class="close-icon">✕</text>`
- **样式**: 添加 `.close-icon` 样式

### 3. 挑战确认页面 (challenge-confirm)
- **文件**: `miniprogram/pages/challenge-confirm/challenge-confirm.wxml`
- **修改**: 
  - `<van-icon name="arrow" />` → `<text class="arrow-icon">→</text>`
- **样式**: 添加 `.arrow-icon` 样式

### 4. 组件配置清理
移除所有页面的 JSON 配置文件中的 `van-icon` 组件引用：
- `miniprogram/pages/wrongbook/wrongbook.json`
- `miniprogram/pages/home/<USER>
- `miniprogram/pages/challenge-confirm/challenge-confirm.json`

## 替换符号对照表
| 原 van-icon | 替换符号 | 用途 |
|-------------|----------|------|
| `name="success"` | ✓ | 成功标识 |
| `name="cross"` | ✕ | 关闭按钮 |
| `name="arrow"` | → | 前进箭头 |

## 技术优势
1. **无网络依赖**: 完全避免外部字体资源加载
2. **兼容性好**: 文本符号在所有设备上都能正确显示
3. **性能优化**: 减少网络请求，提升加载速度
4. **维护简单**: 不需要处理字体文件版本更新

## 测试验证
修复后应验证：
1. 错题集页面功能正常，图标显示清晰
2. 首页弹窗关闭按钮正常工作
3. 挑战确认页面按钮显示正确
4. 不再出现字体加载错误

## 预防措施
为避免类似问题：
1. 优先使用小程序内置组件
2. 避免使用依赖外部资源的第三方组件
3. 使用文本符号或本地图片代替字体图标
4. 定期检查第三方组件的依赖关系 
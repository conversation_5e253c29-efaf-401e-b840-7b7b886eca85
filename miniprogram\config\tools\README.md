# 教材配置索引工具

这是一个用于处理和管理教材配置文件的工具集，包含了一个JavaScript库和命令行工具，可以帮助您方便地查询、搜索和导出教材配置。

## 文件结构

- `indexer.js` - 核心功能库，提供了所有查询和处理功能
- `cli.js` - 命令行工具，提供了便捷的终端操作界面

## 功能特点

1. **数据查询**：轻松获取版本、年级、学期、课程和词表信息
2. **数据统计**：统计教材中的版本、年级、学期、课程和字词数量
3. **内容搜索**：根据关键词搜索字词，快速定位
4. **数据导出**：将词表导出为单独的JSON文件

## 安装依赖

这些工具依赖于Node.js环境。确保您已安装Node.js，然后运行：

```bash
cd miniprogram/config/tools
npm install
```

## 使用方法

### 作为JavaScript库使用

您可以在您的代码中引入`indexer.js`：

```javascript
const indexer = require('./tools/indexer');

// 获取所有版本
const versions = indexer.getAllVersions();
console.log(versions);

// 获取人教版一年级上册第一课的识字表
const table = indexer.findTable('renjiaoban', 'grade1', 'term1', 'lesson1', 'shiZiBiao');
console.log(table);

// 搜索包含"天"的字词
const results = indexer.searchWords('天');
console.log(results);
```

### 作为命令行工具使用

在终端中运行：

```bash
cd miniprogram/config/tools
node cli.js <命令> [参数]
```

可用命令：

- `stats` - 显示教材统计信息
- `versions` - 列出所有版本
- `grades <版本ID>` - 列出指定版本的所有年级
- `terms <版本ID> <年级ID>` - 列出指定版本和年级的所有学期
- `lessons <版本ID> <年级ID> <学期ID>` - 列出指定版本、年级和学期的所有课程
- `table <版本ID> <年级ID> <学期ID> <课程ID> <表类型>` - 显示指定词表内容
- `search <关键词>` - 搜索字词
- `export <版本ID> <年级ID> <学期ID> <课程ID> <表类型>` - 导出词表到文件

表类型可以是：`shiZiBiao`(识字表), `xieZiBiao`(写字表), `ciYuBiao`(词语表)

## 示例

### 显示统计信息

```bash
node cli.js stats
```

输出：
```
教材统计信息:
{
  "versions": 3,
  "grades": 6,
  "terms": 12,
  "lessons": 24,
  "words": {
    "shiZiBiao": 120,
    "xieZiBiao": 72,
    "ciYuBiao": 96
  }
}
```

### 搜索字词

```bash
node cli.js search 天
```

输出：
```
搜索结果 "天":
1. 天 (tiān)
   识字表 - 人教版 一年级上册 第一课 "天地人"
   路径: renjiaoban.grade1.term1.lesson1.shiZiBiao

2. 天空 (tiān kōng)
   词语表 - 人教版 一年级上册 第一课 "天地人"
   路径: renjiaoban.grade1.term1.lesson1.ciYuBiao

...

共找到 12 条匹配结果
```

### 查看特定词表

```bash
node cli.js table renjiaoban grade1 term1 lesson1 shiZiBiao
```

输出：
```
renjiaoban.grade1.term1.lesson1.shiZiBiao 的内容:
- 天: tiān
- 地: dì
- 人: rén
- 你: nǐ
- 我: wǒ
- 他: tā
```

## 扩展使用

这些工具可以很容易地集成到小程序的管理后台或开发工具链中，用于：

1. **开发时的快速查询**：在开发过程中快速查找特定内容
2. **数据验证**：验证配置文件的完整性和正确性
3. **内容批量处理**：对教材内容进行批量更新或修改
4. **导出特定格式**：将数据导出为其他格式，如CSV、Excel等

## 注意事项

- 工具需要在Node.js环境下运行
- 确保配置文件格式正确，符合预期的结构
- 在对配置文件进行修改前，建议先备份原始文件 
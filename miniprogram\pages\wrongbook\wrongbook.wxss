/* 错题本页面样式 */
.wrongbook-page {
  min-height: 100vh;
  background: var(--background-secondary);
  padding-bottom: 32rpx;
}

/* 顶部统计卡片 */
.stats-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  padding: var(--spacing-lg);
  padding-top: calc(88rpx + var(--spacing-lg));
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-main {
  text-align: center;
}

.stats-number {
  font-size: 64rpx;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stats-label {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.stats-detail {
  display: flex;
}

.stats-detail .detail-item {
  margin-right: var(--spacing-lg);
}

.stats-detail .detail-item:last-child {
  margin-right: 0;
}

.detail-item {
  text-align: center;
}

.detail-value {
  display: block;
  font-size: var(--font-size-h3);
  font-weight: 600;
  color: var(--text-primary);
}

.detail-label {
  display: block;
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  margin-top: 4rpx;
}

/* 快速操作按钮 */
.quick-actions {
  display: flex;
}

.quick-actions .action-btn {
  margin-right: var(--spacing-md);
}

.quick-actions .action-btn:last-child {
  margin-right: 0;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-button);
  font-weight: 500;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: white;
  color: var(--primary-color);
  box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.3);
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.action-btn.secondary:active {
  background: #e0e0e0;
}

.action-btn .btn-icon {
  margin-right: var(--spacing-sm);
}

.btn-text {
  font-size: var(--font-size-button);
}

/* 筛选和排序 */
.filter-section {
  background: white;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.filter-tabs {
  display: flex;
  background: var(--background-secondary);
  border-radius: var(--border-radius-md);
  padding: 6rpx;
  margin-bottom: var(--spacing-lg);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.tab-item.active {
  background: white;
  color: var(--primary-color);
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.sort-section {
  display: flex;
  align-items: center;
}

.sort-label {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-right: var(--spacing-md);
}

.sort-picker {
  display: flex;
  align-items: center;
}

.sort-text {
  font-size: var(--font-size-body-sm);
  font-weight: 500;
  margin-right: var(--spacing-xs);
}

.sort-arrow {
  font-size: 20rpx;
  transition: transform 0.3s ease;
}

/* 错题列表 */
.wrong-list {
  padding: 0 var(--spacing-lg);
}

.word-item {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
}

.word-item:active {
  transform: scale(0.98);
}

.word-main {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-content {
  display: flex;
  align-items: center;
}

.word-text {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: var(--spacing-md);
}

.word-pinyin {
  font-size: 22rpx;
  color: #999;
  margin-left: 16rpx;
}

.word-info {
  text-align: right;
}

.error-count {
  display: flex;
  align-items: center;
}

.count-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

.count-text {
  font-size: var(--font-size-body-sm);
  color: var(--warning-color);
  font-weight: 500;
}

.last-error {
  font-size: var(--font-size-caption);
  color: var(--text-light);
}

.word-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  margin-left: var(--spacing-lg);
}

.status-badge {
  padding: 4rpx var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  font-weight: 500;
}

.status-badge.unmastered {
  background: var(--error-color);
  color: white;
}

.status-badge.learning {
  background: var(--warning-color);
  color: white;
}

.status-badge.mastered {
  background: var(--success-color);
  color: white;
}

.practice-btn {
  width: 60rpx;
  height: 60rpx;
  background: var(--primary-color);
  border-radius: var(--border-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.practice-icon {
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 48rpx;
  display: block;
}

/* 字词详情弹窗 */
.word-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-xl);
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(100rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-display {
  text-align: center;
}

.display-word {
  font-size: 72rpx;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.display-pinyin {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 32rpx;
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: var(--spacing-lg);
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--font-size-h3);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.error-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--background-secondary);
  border-radius: var(--border-radius-md);
}

.stat-label {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: var(--font-size-body);
  font-weight: 600;
  color: var(--text-primary);
}

.error-history {
  space: var(--spacing-md);
}

.history-item {
  padding: var(--spacing-md);
  background: var(--background-secondary);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-sm);
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-date {
  font-size: var(--font-size-caption);
  color: var(--text-light);
  margin-bottom: 4rpx;
}

.history-answer {
  font-size: var(--font-size-body-sm);
  color: var(--text-primary);
}

.word-meaning {
  font-size: var(--font-size-body);
  color: var(--text-primary);
  line-height: 1.6;
  background: var(--background-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 2rpx solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-button);
  font-weight: 500;
}

.modal-btn::after {
  border: none;
}

.modal-btn.primary {
  background: var(--primary-color);
  color: white;
}

.modal-btn.secondary {
  background: var(--background-secondary);
  color: var(--text-secondary);
}

/* 练习模式选择弹窗 */
.practice-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.practice-content {
  background: white;
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
  width: 100%;
  animation: slideUp 0.3s ease-out;
  padding-bottom: 32rpx;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.practice-header {
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.practice-title {
  font-size: var(--font-size-h3);
  font-weight: 600;
  color: var(--text-primary);
}

.practice-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 32rpx;
}

.practice-options {
  padding: var(--spacing-lg);
}

.option-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  transition: background-color 0.3s ease;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item:active {
  background: var(--border-color);
}

.option-icon {
  font-size: 48rpx;
  margin-right: var(--spacing-lg);
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: var(--font-size-body);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

/* 错题集页面样式 */
.wrongbook-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 32rpx;
}

/* 标签页导航 */
.tabs-nav {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #e8e8e8;
  padding-top: 20rpx; /* 增加顶部内边距，弥补移除头部统计的空间 */
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #1890ff;
  border-radius: 2rpx;
}

/* 标签页内容 */
.tab-content {
  background: #f5f5f5;
  min-height: calc(100vh - 200rpx);
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* 操作栏 */
.action-bar {
  background: white;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e8e8e8;
}

.selected-info {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin: 0 24rpx;
}

/* 复选框样式 */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #1890ff;
  border-color: #1890ff;
}

.checkbox-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333;
}

/* 按钮样式 */
.btn-primary {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.btn-primary.disabled {
  background: #d9d9d9;
  color: #999;
}

.btn-mini {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border-radius: 6rpx;
  margin-left: 8rpx;
  border: none;
}

.btn-info {
  background: #1890ff;
  color: white;
}

.btn-warning {
  background: #fa8c16;
  color: white;
}

/* 错题列表 */
.error-list {
  padding: 0 32rpx;
}

.course-group {
  margin-bottom: 32rpx;
}

.course-header {
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2rpx solid #dee2e6;
  margin-bottom: 24rpx;
}

.course-header.history {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.course-count {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

/* 字词列表 */
.word-list {
  background: white;
  border-radius: 0 0 12rpx 12rpx;
  overflow: hidden;
}

.word-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.3s ease;
}

.word-item:last-child {
  border-bottom: none;
}

.word-item.history {
  cursor: pointer;
}

.word-item.history:active {
  background: #f5f5f5;
}

.word-content {
  flex: 1;
  margin-left: 16rpx;
}

.word-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.word-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.word-pinyin {
  font-size: 22rpx;
  color: #999;
  margin-left: 16rpx;
}

.word-meta {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  width: 100%;
}

.error-count {
  font-size: 22rpx;
  color: #f44336;
  font-weight: 500;
  margin-bottom: 2rpx;
}

.error-time,
.corrected-time {
  font-size: 18rpx;
  color: #999;
  text-align: center;
}

.word-actions {
  position: absolute;
  bottom: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;
}

.word-actions .van-button {
  font-size: 18rpx !important;
  padding: 4rpx 8rpx !important;
  min-width: 60rpx !important;
  height: 28rpx !important;
}

/* 字词网格布局 */
.words-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.word-grid-item {
  position: relative;
  width: 160rpx;
  height: 180rpx;
  background: white;
  border-radius: var(--border-radius-md);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: visible;
}

.word-grid-item.selected {
  border-color: var(--primary-color);
  background: var(--primary-color-light);
  transform: scale(1.05);
}

.word-grid-item.history {
  background: var(--success-color-light);
  border-color: var(--success-color);
  cursor: default;
}

/* 田字格背景 */
.word-background {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-xs);
}

.word-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2rpx dashed #E0E6ED;
  border-radius: 8rpx;
}

.word-background::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2rpx;
  height: 100%;
  background: #E0E6ED;
  transform: translate(-50%, -50%);
}

.word-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
  line-height: 1;
  margin-bottom: 4rpx;
}

.word-pinyin {
  font-size: 20rpx;
  color: var(--text-secondary);
  position: relative;
  z-index: 2;
  text-align: center;
  line-height: 1;
}

/* 选中标记 */
.word-check {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-round);
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 已订正标记 */
.word-corrected {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #4caf50;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16rpx;
  opacity: 0.9;
  z-index: 5;
}

/* 未订正标记 */
.word-pending {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #ff9800;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16rpx;
  opacity: 0.9;
  z-index: 5;
}

/* 字词元信息 */
.word-meta {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  right: 8rpx;
  text-align: center;
}

.error-count {
  font-size: 16rpx;
  color: #ff9800;
  position: absolute;
  bottom: 6rpx;
  left: 6rpx;
  background: rgba(255, 255, 255, 0.8);
  padding: 0 4rpx;
  border-radius: 4rpx;
  z-index: 4;
}

.error-time, .corrected-time {
  font-size: 16rpx;
  color: var(--text-light);
  display: block;
}

/* 状态标识 */
.word-status {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.status-tag {
  font-size: 14rpx;
  padding: 2rpx 6rpx;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  display: inline-block;
  max-width: 80rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-tag.corrected {
  background: var(--success-color);
  color: white;
}

.status-tag.uncorrected {
  background: var(--warning-color);
  color: white;
}

/* 田字格样式 */
.word-grid-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16rpx;
  padding-top: 32rpx; /* 为拼音留出空间 */
}

.tianzige {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  margin: 0 4rpx;
  background-color: #fff;
  border: 1px solid #ddd;
}

.tianzige::before,
.tianzige::after {
  content: '';
  position: absolute;
  background-color: #f0f0f0;
}

/* 横线 */
.tianzige::before {
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  transform: translateY(-50%);
}

/* 竖线 */
.tianzige::after {
  left: 50%;
  top: 0;
  bottom: 0;
  width: 1px;
  transform: translateX(-50%);
}

.tianzige-inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  z-index: 1;
}

/* 单个字符的拼音样式 */
.character-pinyin {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  font-size: 18rpx;
  color: #666;
  line-height: 1.2;
  height: 24rpx;
  z-index: 2;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4rpx;
  padding: 2rpx 4rpx;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  font-weight: 500;
  min-width: 20rpx;
  overflow: visible;
}

/* 已订正和未订正标记 - 调整大小和位置 */
.word-corrected {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #4caf50;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14rpx;
  opacity: 0.85;
  z-index: 5;
}

.word-pending {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #ff9800;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14rpx;
  opacity: 0.85;
  z-index: 5;
}

/* 错误次数信息 - 调整位置避免与田字格重叠 */
.error-count {
  font-size: 16rpx;
  color: #ff9800;
  position: absolute;
  bottom: 6rpx;
  left: 6rpx;
  background: rgba(255, 255, 255, 0.8);
  padding: 0 4rpx;
  border-radius: 4rpx;
  z-index: 4;
  box-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

/* 字词状态标签调整 */
.word-status {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  z-index: 3;
}

.status-tag {
  font-size: 12rpx;
  padding: 2rpx 4rpx;
  border-radius: 4rpx;
  font-weight: 500;
  display: inline-block;
  max-width: 60rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.85;
}

/* 历史记录字词项点击提示 */
.word-grid-item.history {
  cursor: pointer;
  transition: all 0.2s ease;
}

.word-grid-item.history:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.word-grid-item.history:active {
  transform: translateY(0);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 笔画动画弹窗样式 */
.stroke-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.stroke-modal-mask.show {
  opacity: 1;
  visibility: visible;
}

.stroke-modal-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 85vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.stroke-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #1890ff 0%, #667eea 100%);
  color: white;
}

.stroke-modal-header .modal-title {
  font-size: 36rpx;
  font-weight: 600;
}

.stroke-modal-header .close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  transition: background 0.2s ease;
}

.stroke-modal-header .close-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 字符切换器 */
.character-switcher {
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.switcher-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.character-tabs {
  display: flex;
  gap: 16rpx;
}

.character-tab {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  background: white;
  transition: all 0.2s ease;
}

.character-tab.active {
  border-color: #1890ff;
  background: #1890ff;
  color: white;
}

.character-tab:not(.active):active {
  background: #f5f5f5;
}

/* 汉字书写器容器 */
.hanzi-writer-container {
  padding: 0; /* 移除padding，避免影响子元素定位 */
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff; /* 白色背景更干净 */
  position: relative;
  width: 300px;
  height: 300px;
  margin: 40rpx auto; /* 保持外边距 */
  border-radius: 8px;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 添加轻微阴影增强立体感 */
}

.grid-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-radius: 8px;
  background: white !important;
  width: 300px !important;
  height: 300px !important;
  display: block !important;
}

.hanzi-writer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  background: transparent !important;
  width: 300px;
  height: 300px;
  transition: opacity 0.3s ease;
}

/* 显示和隐藏状态 */
.hanzi-writer-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.hanzi-writer-overlay.hide {
  opacity: 0;
  pointer-events: none;
}

/* 确保 hanzi-writer-view 内部的 canvas 也是透明的 */
.hanzi-writer-overlay canvas {
  background: transparent !important;
}

/* 字符信息 */
.character-info {
  padding: 32rpx;
  text-align: center;
  border-bottom: 2rpx solid #f0f0f0;
  display: none; /* 隐藏字符信息区域 */
}

.current-character {
  font-size: 80rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16rpx;
}

.character-description {
  font-size: 28rpx;
  color: #666;
}

/* 操作按钮 */
.stroke-modal-actions {
  padding: 32rpx;
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

.action-btn.primary:active {
  background: #5a67d8;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.action-btn.secondary:active {
  background: #e0e0e0;
}
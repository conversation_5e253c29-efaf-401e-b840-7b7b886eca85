# TabBar 图标修复说明

## 问题描述
```
[ app.json 文件内容错误] app.json: ["tabBar"]["list"][0]["iconPath"]: "images/home.png" not found
["tabBar"]["list"][0]["selectedIconPath"]: "images/home-active.png" not found
["tabBar"]["list"][1]["iconPath"]: "images/wrong.png" not found
["tabBar"]["list"][1]["selectedIconPath"]: "images/wrong-active.png" not found
```

## 问题原因
`app.json` 中配置了 TabBar 图标文件路径，但项目中缺少对应的图标文件：
- `images/home.png` - 首页图标
- `images/home-active.png` - 首页选中状态图标
- `images/wrong.png` - 错题集图标
- `images/wrong-active.png` - 错题集选中状态图标

## 解决方案
采用**移除图标配置**的方式，改为使用纯文本 TabBar：

### 修改内容
在 `miniprogram/app.json` 中：

**修改前：**
```json
"list": [
  {
    "pagePath": "pages/home/<USER>",
    "text": "首页",
    "iconPath": "images/home.png",
    "selectedIconPath": "images/home-active.png"
  },
  {
    "pagePath": "pages/wrongbook/wrongbook",
    "text": "错题集",
    "iconPath": "images/wrong.png",
    "selectedIconPath": "images/wrong-active.png"
  }
]
```

**修改后：**
```json
"list": [
  {
    "pagePath": "pages/home/<USER>",
    "text": "首页"
  },
  {
    "pagePath": "pages/wrongbook/wrongbook",
    "text": "错题集"
  }
]
```

## 技术优势

### 1. 简化维护
- **无需图标文件**：不需要设计和维护图标资源
- **减少打包体积**：移除图标文件可减少小程序包大小
- **避免适配问题**：不需要考虑不同分辨率的图标适配

### 2. 一致性更好
- **统一风格**：与我们移除 `van-icon` 的策略保持一致
- **减少依赖**：进一步减少外部资源依赖
- **更简洁**：纯文字 TabBar 更加简洁明了

### 3. 兼容性强
- **系统原生**：使用小程序原生文字 TabBar
- **加载快速**：无需等待图标加载
- **适应性强**：自动适应系统主题

## 替代方案（可选）

如果将来需要图标，可以考虑以下方案：

### 方案一：创建简单图标
```bash
# 创建图标目录
mkdir miniprogram/images

# 使用设计工具创建以下尺寸的图标：
# - 81px × 81px（普通状态）
# - 81px × 81px（选中状态）
```

### 方案二：使用 emoji 替代
```json
{
  "pagePath": "pages/home/<USER>",
  "text": "🏠 首页"
},
{
  "pagePath": "pages/wrongbook/wrongbook", 
  "text": "📝 错题集"
}
```

## 测试验证

修复后验证项目：
1. ✅ 小程序编译无错误
2. ✅ TabBar 正常显示文字
3. ✅ 页面切换功能正常
4. ✅ 颜色配置生效（选中/未选中）

## 总结

通过移除 TabBar 图标配置，我们：
- 解决了文件缺失错误
- 保持了应用的简洁性
- 提高了加载性能
- 减少了维护成本

这与我们之前修复字体加载错误的策略一致，都是通过简化依赖来提高应用的稳定性和性能。 
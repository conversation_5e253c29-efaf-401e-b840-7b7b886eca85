# 挑战确认页设计

## 1. 概述

挑战确认页是用户选择课程后，在正式开始学习前的一个准备环节。该页面允许用户查看课程详情，并从识字表、写字表和词语表中选择要挑战的字词。用户可以灵活选择挑战内容，包括全选某一类表或单独选择特定字词，最后确认开始挑战。

## 2. 页面结构

### 2.1 顶部栏

顶部栏显示当前选中课程的总览信息，包含：

- 课程名称（如"第一课：天地人"）
- 教材信息（如"人教版 一年级上册"）
- 字词总数统计（如"共30个字词"）
- 返回按钮（返回课程列表页）

### 2.2 主体内容区

主体内容区采用垂直滚动列表结构，依次展示三类字词表：

1. **识字表区块**：显示该课程识字表中的所有汉字
2. **写字表区块**：显示该课程写字表中的所有汉字
3. **词语表区块**：显示该课程词语表中的所有词语

每个区块上方有标题和全选/取消全选的控制按钮，各表之间有明显的视觉分隔。用户可通过垂直滚动查看所有内容，无需切换标签页。

### 2.3 底部操作栏

底部固定操作栏，包含：

- 已选择字词数量提示（如"已选择: 12/30"）
- 开始挑战按钮（仅在选择了至少一个字词时可点击）

## 3. 交互设计

### 3.1 字词选择

- **区块全选按钮**：点击可选中当前区块（识字表/写字表/词语表）下所有字词
- **单个字词选择**：点击单个字词方块可切换选中/取消状态
- **视觉反馈**：选中的字词有明显的视觉标记（如背景色变化、勾选标记）
- **垂直滚动**：用户可通过垂直滚动查看所有区块的内容，选择状态会被保存

### 3.2 开始挑战

- 当用户选择了至少一个字词后，开始挑战按钮变为可点击状态
- 点击开始挑战按钮后，进入挑战页面，并传递已选择的字词数据

## 4. UI设计

### 4.1 顶部栏

```
+-----------------------------------------------+
| ← | 第一课：天地人     人教版一年级上册 共30字 |
+-----------------------------------------------+
```

### 4.2 内容区块结构

```
+-----------------------------------------------+
| 【识字表】                □ 全选（10字）      |
+-----------------------------------------------+
| [天√] [地√] [人√] [你 ] [我 ] [他 ]          |
| [  ] [  ] [  ] [  ]                         |
+-----------------------------------------------+
| 【写字表】                □ 全选（6字）       |
+-----------------------------------------------+
| [一√] [二 ] [三 ] [四 ] [五 ] [六 ]          |
+-----------------------------------------------+
| 【词语表】                □ 全选（8词）       |
+-----------------------------------------------+
| [天空√] [大地 ] [人们 ] [学校 ]               |
| [早上 ] [来到 ] [小鸟 ] [金鱼 ]               |
+-----------------------------------------------+
```

### 4.3 字词块设计

每个字词块的设计如下：

```
+----------------+     +----------------+
|      天        |     |      地 ✓      |
|     tiān       |     |      dì        |
+----------------+     +----------------+
    未选中状态             已选中状态
```

字词块包含以下元素：
- 汉字/词语（主体显示）
- 拼音（小字显示在下方）
- 选中状态标记（已选中时显示✓或背景色变化）
- 触摸反馈效果（按下时的视觉反馈）

对于不同类型的表格，字词块样式略有调整：
- 识字表/写字表：使用方形设计，突出单个汉字
- 词语表：使用长方形设计，适应多字词语

### 4.4 底部操作栏

```
+-----------------------------------------------+
| 已选择: 12/30            [开始挑战 →]         |
+-----------------------------------------------+
```

## 5. 数据交互

### 5.1 页面加载数据

```javascript
{
  "course": {
    "id": "lesson1",
    "name": "第一课",
    "title": "天地人",
    "publisher": "人教版",
    "grade": "一年级",
    "term": "上册"
  },
  "tables": {
    "shiZiBiao": [
      { "id": 1, "word": "天", "pinyin": "tiān", "selected": false },
      { "id": 2, "word": "地", "pinyin": "dì", "selected": false },
      // ...更多汉字
    ],
    "xieZiBiao": [
      { "id": 1, "word": "一", "pinyin": "yī", "selected": false },
      // ...更多汉字
    ],
    "ciYuBiao": [
      { "id": 1, "word": "天空", "pinyin": "tiān kōng", "selected": false },
      // ...更多词语
    ]
  }
}
```

### 5.2 提交数据

```javascript
{
  "courseId": "lesson1",
  "selectedWords": [
    { "id": 1, "word": "天", "pinyin": "tiān", "table": "shiZiBiao" },
    { "id": 2, "word": "地", "pinyin": "dì", "table": "shiZiBiao" },
    { "id": 1, "word": "一", "pinyin": "yī", "table": "xieZiBiao" },
    // ...更多已选择的字词
  ]
}
```

## 6. 核心组件

### 6.1 CourseHeader 组件

显示课程基本信息，包括名称、教材版本、统计数据等。

### 6.2 WordTableSection 组件

字词表区块，包含区块标题、全选按钮和字词网格，用于展示不同类型的字词表。每种表类型（识字表/写字表/词语表）创建一个该组件实例。

### 6.3 WordSelectionGrid 组件

字词选择网格，以方块形式展示字词，支持单选功能。

### 6.4 ActionBar 组件

底部操作栏，显示选择统计和开始挑战按钮。

## 7. 状态管理

- **selectedWords**：已选择的字词ID列表，按表类型分组
- **selectionStats**：选择统计信息（已选数量/总数量）
- **tableVisibility**：各表区块的可见性状态（用于优化性能，可选实现）

## 8. 响应式设计

- 在小屏幕设备上，字词网格自动调整为更小的尺寸和更多的列
- 保证字词块的最小尺寸不小于48px，确保触摸友好
- 区块标题始终保持可见，即使字词内容需要滚动

## 9. 交互细节

- 选中字词时有轻微动画和触感反馈
- 全选按钮状态随已选字词数量变化（全选/部分选中/未选）
- 滚动到不同区块时有轻微的视差效果或平滑过渡
- 底部操作栏固定在屏幕底部，不随内容滚动
- 可添加快速跳转到不同表区块的侧边导航（可选）

## 10. 后续交互流

- 点击"开始挑战"按钮后，系统将创建一个挑战会话，包含所选字词
- 进入挑战页面，开始字词听写练习
- 若用户未选择任何字词，开始挑战按钮将保持禁用状态 
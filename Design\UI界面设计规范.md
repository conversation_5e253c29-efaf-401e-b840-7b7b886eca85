# 小学字词听写软件UI界面设计规范

## 1. 设计理念

### 1.1 设计原则
- **简洁优先**：界面简洁明了，减少认知负担
- **活泼有趣**：色彩明快，充满童趣和活力
- **易于操作**：大按钮设计，适合小学生手指操作
- **视觉友好**：护眼配色，减少视觉疲劳
- **一致性**：统一的视觉语言和交互模式

### 1.2 目标用户特征
- **年龄群体**：6-12岁小学生
- **操作习惯**：触摸为主，简单直观
- **注意力特点**：易分散，需要视觉引导
- **喜好偏向**：色彩丰富，卡通可爱元素

### 1.3 设计目标
- 提供愉悦的学习体验
- 降低使用门槛和学习成本
- 增强学习动机和参与度
- 营造轻松活泼的学习氛围

## 2. 色彩系统

### 2.1 主色调设计
```css
/* 主色调：清新蓝色系 */
--primary-color: #4A90E2;      /* 主蓝色 - 专业可信 */
--primary-light: #6BA6FF;      /* 浅蓝色 - 活泼清新 */
--primary-dark: #357ABD;       /* 深蓝色 - 稳重可靠 */

/* 辅助色：活泼暖色系 */
--secondary-color: #FFB74D;    /* 温暖橙色 - 鼓励激励 */
--accent-color: #81C784;       /* 清新绿色 - 成功正确 */
--warning-color: #FF8A65;      /* 温和红色 - 错误警告 */

/* 中性色：柔和灰色系 */
--text-primary: #2C3E50;       /* 主文字色 */
--text-secondary: #7F8C8D;     /* 次要文字色 */
--text-light: #BDC3C7;         /* 浅色文字 */
--background-primary: #FFFFFF; /* 主背景色 */
--background-secondary: #F8F9FA; /* 次要背景色 */
--border-color: #E5E5E5;       /* 边框颜色 */
```

### 2.2 色彩应用规则
- **主色调**：导航、按钮、重要操作元素
- **辅助色**：提示、奖励、成就等正向反馈
- **警告色**：错误提示、注意事项
- **中性色**：文字、背景、分割线

### 2.3 色彩搭配示例
```
成功状态：绿色 (#81C784) + 白色文字
警告状态：橙色 (#FFB74D) + 深色文字  
错误状态：红色 (#FF8A65) + 白色文字
信息状态：蓝色 (#4A90E2) + 白色文字
```

## 3. 字体规范

### 3.1 字体选择
- **中文字体**：系统默认字体 (苹方/微软雅黑/思源黑体)
- **英文字体**：-apple-system, BlinkMacSystemFont, 'Segoe UI'
- **数字字体**：DIN, Helvetica Neue (确保数字清晰易读)

### 3.2 字体大小层级
```css
/* 标题层级 */
--font-size-h1: 32rpx;   /* 主标题 */
--font-size-h2: 28rpx;   /* 二级标题 */
--font-size-h3: 24rpx;   /* 三级标题 */

/* 正文层级 */
--font-size-body: 28rpx;    /* 正文内容 */
--font-size-body-sm: 24rpx; /* 小号正文 */
--font-size-caption: 20rpx; /* 说明文字 */

/* 特殊字体 */
--font-size-large: 40rpx;   /* 大号显示（如分数） */
--font-size-button: 28rpx;  /* 按钮文字 */
```

### 3.3 字重规范
- **粗体**：700 - 用于标题和重要信息
- **中等**：500 - 用于按钮和强调文本
- **正常**：400 - 用于正文内容
- **细体**：300 - 用于辅助说明

### 3.4 行高设置
```css
--line-height-compact: 1.2;  /* 紧凑行高 - 标题 */
--line-height-normal: 1.5;   /* 正常行高 - 正文 */
--line-height-loose: 1.8;    /* 松散行高 - 易读文本 */
```

## 4. 布局规范

### 4.1 间距系统
```css
/* 基础间距单位 */
--spacing-xs: 8rpx;    /* 极小间距 */
--spacing-sm: 16rpx;   /* 小间距 */
--spacing-md: 24rpx;   /* 中等间距 */
--spacing-lg: 32rpx;   /* 大间距 */
--spacing-xl: 48rpx;   /* 极大间距 */

/* 页面间距 */
--page-padding: 32rpx;      /* 页面左右边距 */
--section-spacing: 48rpx;   /* 区块间距 */
--content-spacing: 24rpx;   /* 内容间距 */
```

### 4.2 网格系统
- **容器宽度**：750rpx（小程序标准宽度）
- **列数**：12列网格系统
- **间距**：24rpx
- **响应式**：自适应不同屏幕尺寸

### 4.3 布局模式
```css
/* 卡片布局 */
.card {
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    margin: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

/* 列表布局 */
.list-item {
    padding: 24rpx 32rpx;
    border-bottom: 2rpx solid #F0F0F0;
    min-height: 88rpx;
}

/* 表单布局 */
.form-item {
    margin-bottom: 32rpx;
    padding: 0 32rpx;
}
```

## 5. 组件设计规范

### 5.1 按钮组件
```css
/* 主要按钮 */
.btn-primary {
    background: linear-gradient(135deg, #4A90E2 0%, #6BA6FF 100%);
    color: #FFFFFF;
    border-radius: 48rpx;
    height: 88rpx;
    font-size: 28rpx;
    font-weight: 500;
    box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

/* 次要按钮 */
.btn-secondary {
    background: #FFFFFF;
    color: #4A90E2;
    border: 2rpx solid #4A90E2;
    border-radius: 48rpx;
    height: 88rpx;
    font-size: 28rpx;
}

/* 圆形图标按钮 */
.btn-icon {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    background: #FFB74D;
    display: flex;
    align-items: center;
    justify-content: center;
}
```

### 5.2 卡片组件
```css
.card-basic {
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #F5F5F5;
}

.card-interactive {
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card-interactive:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
}
```

### 5.3 输入组件
```css
.input-field {
    height: 88rpx;
    background: #F8F9FA;
    border: 2rpx solid #E5E5E5;
    border-radius: 12rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #2C3E50;
}

.input-field:focus {
    border-color: #4A90E2;
    background: #FFFFFF;
    box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

/* 手写输入区域 */
.handwriting-canvas {
    background: #FFFFFF;
    border: 4rpx dashed #4A90E2;
    border-radius: 16rpx;
    min-height: 400rpx;
    position: relative;
}
```

### 5.4 进度组件
```css
.progress-bar {
    height: 12rpx;
    background: #F0F0F0;
    border-radius: 6rpx;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4A90E2 0%, #81C784 100%);
    border-radius: 6rpx;
    transition: width 0.3s ease;
}

/* 圆形进度条 */
.progress-circle {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: conic-gradient(#4A90E2 0deg, #F0F0F0 0deg);
}
```

## 6. 图标系统

### 6.1 图标风格
- **风格**：线条图标为主，填充图标为辅
- **粗细**：2rpx线条粗细
- **圆角**：4rpx圆角处理
- **尺寸**：32rpx, 48rpx, 64rpx三种常用尺寸

### 6.2 图标分类
```css
/* 功能图标 */
.icon-play { color: #4A90E2; }      /* 播放 */
.icon-edit { color: #FFB74D; }      /* 编辑 */
.icon-check { color: #81C784; }     /* 正确 */
.icon-close { color: #FF8A65; }     /* 错误 */
.icon-star { color: #FFD54F; }      /* 收藏/成就 */

/* 状态图标 */
.icon-success { color: #81C784; }   /* 成功 */
.icon-warning { color: #FFB74D; }   /* 警告 */
.icon-error { color: #FF8A65; }     /* 错误 */
.icon-info { color: #4A90E2; }      /* 信息 */
```

### 6.3 图标使用规则
- 保持统一的视觉风格
- 确保在不同背景下的可识别性
- 合理使用色彩表达不同状态
- 提供足够的点击区域（至少88rpx）

## 7. 动效规范

### 7.1 过渡动画
```css
/* 基础过渡 */
.transition-fast { transition: all 0.2s ease; }
.transition-normal { transition: all 0.3s ease; }
.transition-slow { transition: all 0.5s ease; }

/* 弹性动画 */
.bounce-in {
    animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}
```

### 7.2 反馈动画
```css
/* 成功反馈 */
.success-pulse {
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { background-color: #81C784; transform: scale(1); }
    50% { background-color: #A5D6A7; transform: scale(1.05); }
    100% { background-color: #81C784; transform: scale(1); }
}

/* 错误摇摆 */
.error-shake {
    animation: errorShake 0.5s ease;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10rpx); }
    75% { transform: translateX(10rpx); }
}
```

### 7.3 加载动画
```css
.loading-spinner {
    width: 48rpx;
    height: 48rpx;
    border: 4rpx solid #F0F0F0;
    border-top: 4rpx solid #4A90E2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 8. 页面布局模板

### 8.1 标准页面模板
```html
<view class="page">
  <view class="page-header">
    <view class="header-title">页面标题</view>
    <view class="header-actions">操作按钮</view>
  </view>
  
  <view class="page-content">
    <view class="content-section">
      <!-- 主要内容区域 -->
    </view>
  </view>
  
  <view class="page-footer">
    <button class="btn-primary">主要操作</button>
  </view>
</view>
```

### 8.2 卡片列表模板
```html
<view class="card-list">
  <view class="card-item" wx:for="{{items}}">
    <view class="card-icon">
      <image src="{{item.icon}}" />
    </view>
    <view class="card-content">
      <view class="card-title">{{item.title}}</view>
      <view class="card-desc">{{item.description}}</view>
    </view>
    <view class="card-action">
      <text class="action-text">进入</text>
    </view>
  </view>
</view>
```

### 8.3 表单页面模板
```html
<view class="form-page">
  <view class="form-content">
    <view class="form-item">
      <view class="form-label">字段标签</view>
      <input class="form-input" placeholder="请输入内容" />
    </view>
  </view>
  
  <view class="form-actions">
    <button class="btn-secondary">取消</button>
    <button class="btn-primary">确认</button>
  </view>
</view>
```

## 9. 响应式设计

### 9.1 屏幕适配
```css
/* 小屏幕适配 (iPhone SE) */
@media (max-width: 375px) {
    .page-padding { padding: 24rpx; }
    .btn-primary { height: 80rpx; font-size: 26rpx; }
}

/* 大屏幕适配 (iPad) */
@media (min-width: 768px) {
    .container { max-width: 600rpx; margin: 0 auto; }
}
```

### 9.2 字体缩放适配
```css
/* 支持系统字体缩放 */
.scalable-text {
    font-size: calc(28rpx + 2 * (100vw - 750rpx) / 750);
}
```

## 10. 可访问性设计

### 10.1 颜色对比度
- 确保文字与背景对比度 ≥ 4.5:1
- 重要信息不仅依赖颜色传达
- 提供高对比度模式选项

### 10.2 触控区域
- 按钮最小点击区域：88rpx × 88rpx
- 重要操作按钮间距 ≥ 16rpx
- 提供清晰的点击反馈

### 10.3 文字可读性
- 关键信息使用足够大的字体
- 提供字体大小调节选项
- 避免使用过于复杂的字体

## 11. 品牌元素

### 11.1 Logo规范
- **主logo**：用于启动页、关于页面
- **图标logo**：用于小程序图标、favicon
- **文字logo**：用于需要横向排列的场景

### 11.2 吉祥物设计
- **形象**：可爱的小学生形象或学习相关动物
- **用途**：引导页、空状态、鼓励反馈
- **风格**：简洁可爱，符合品牌调性

### 11.3 插画风格
- **风格**：扁平化插画，线条简洁
- **色彩**：与主色调保持一致
- **应用**：功能引导、空状态、成就奖励

## 12. 设计交付规范

### 12.1 设计稿规范
- **尺寸**：750px宽度（2倍图）
- **格式**：Sketch/Figma源文件 + PNG导出
- **命名**：功能模块_页面名称_状态.png
- **标注**：详细的尺寸、间距、颜色标注

### 12.2 切图规范
- **格式**：PNG/SVG
- **尺寸**：@1x, @2x, @3x三套
- **压缩**：适当压缩，保证清晰度
- **命名**：icon_功能名称_状态@2x.png

### 12.3 组件库维护
- 建立设计组件库
- 定期更新组件样式
- 保持设计与开发同步
- 提供详细的使用文档 
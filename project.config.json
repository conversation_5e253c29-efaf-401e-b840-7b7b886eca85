{"description": "小学字词听写软件", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}]}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": true, "enhance": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "enableEngineNative": false, "packNpmRelationList": [{"packageJsonPath": "./miniprogram/package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx1234567890abcdef", "projectname": "wordapp", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}
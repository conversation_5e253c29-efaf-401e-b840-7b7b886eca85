# 界面风格展示

## 1. 设计理念

小学字词听写软件的界面设计以"简洁、活泼、易用"为核心理念，创造一个既专业又充满童趣的学习环境。

### 1.1 设计原则
- **简洁明了**：信息层次清晰，减少认知负担
- **活泼童趣**：色彩温馨，图标生动，符合儿童审美
- **易用友好**：操作简单，反馈及时，容错性强
- **教育导向**：突出学习功能，营造专注的学习氛围

### 1.2 目标用户体验
- 让孩子感到有趣而不分散注意力
- 让家长感到专业而值得信赖
- 让老师感到实用而高效便捷

## 2. 色彩系统展示

### 2.1 主色调系统
```
┌─────────────────────────────────────────┐
│                                        │
│  🎨 主色调系统                          │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #4A90E2 │ │ #357ABD │ │ #2E6DA4 │   │
│  │ 主蓝色   │ │ 深蓝色   │ │ 深海蓝   │   │
│  │ 学习     │ │ 专注     │ │ 稳重     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  用途：导航栏、按钮主色、学习进度        │
│  象征：专业、稳重、值得信赖              │
│                                        │
└─────────────────────────────────────────┘
```

### 2.2 辅助色彩系统
```
┌─────────────────────────────────────────┐
│                                        │
│  🌈 辅助色彩                            │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #FF9500 │ │ #FF6B35 │ │ #FFA726 │   │
│  │ 活力橙   │ │ 热情橙   │ │ 温暖橙   │   │
│  │ 奖励     │ │ 错误     │ │ 提醒     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #52C41A │ │ #73D13D │ │ #95DE64 │   │
│  │ 成功绿   │ │ 完成绿   │ │ 清新绿   │   │
│  │ 正确     │ │ 达成     │ │ 成长     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #F5222D │ │ #CF1322 │ │ #A8071A │   │
│  │ 警告红   │ │ 错误红   │ │ 危险红   │   │
│  │ 注意     │ │ 错误     │ │ 删除     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
└─────────────────────────────────────────┘
```

### 2.3 中性色彩系统
```
┌─────────────────────────────────────────┐
│                                        │
│  ⚫ 中性色系                            │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #262626 │ │ #434343 │ │ #595959 │   │
│  │ 标题黑   │ │ 正文灰   │ │ 辅助灰   │   │
│  │ 18px     │ │ 16px     │ │ 14px     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #8C8C8C │ │ #BFBFBF │ │ #F0F0F0 │   │
│  │ 禁用灰   │ │ 边框灰   │ │ 背景灰   │   │
│  │ 12px     │ │ 分割线   │ │ 区域背景 │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ #FAFAFA │ │ #FFFFFF │ │ #F5F5F5 │   │
│  │ 浅背景   │ │ 纯白     │ │ 卡片背景 │   │
│  │ 页面     │ │ 主内容   │ │ 组件     │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
└─────────────────────────────────────────┘
```

## 3. 字体系统展示

### 3.1 字体层级
```
┌─────────────────────────────────────────┐
│                                        │
│  📝 字体层级系统                        │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 特大标题 - 24px/粗体                │ │
│  │ 用于页面主标题                      │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 大标题 - 20px/粗体                  │ │
│  │ 用于模块标题、重要信息              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 中标题 - 18px/中等                  │ │
│  │ 用于子标题、卡片标题                │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 正文 - 16px/常规                    │ │
│  │ 用于主要内容、说明文字              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 小字 - 14px/常规                    │ │
│  │ 用于辅助信息、状态说明              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 标注 - 12px/常规                    │ │
│  │ 用于标签、时间、数量统计            │ │
│  └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

### 3.2 特殊字体应用
```
┌─────────────────────────────────────────┐
│                                        │
│  ✏️ 练习字体 - 楷体/28px                │
│  用于汉字展示、书写示范                  │
│                                        │
│       苹  果  温  暖                   │
│                                        │
│  🔤 拼音字体 - 微软雅黑/14px            │
│  用于拼音标注                          │
│                                        │
│    píng guǒ  wēn nuǎn                  │
│                                        │
│  🔢 数字字体 - DIN/16px                 │
│  用于分数、统计、时间                    │
│                                        │
│      85分  08:45  15/20                │
│                                        │
└─────────────────────────────────────────┘
```

## 4. 组件设计展示

### 4.1 按钮组件系统
```
┌─────────────────────────────────────────┐
│                                        │
│  🔘 主要按钮 (Primary)                  │
│  ┌─────────────────────────────────────┐ │
│  │           开始学习                  │ │
│  │      [#4A90E2背景，白字]           │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🔘 次要按钮 (Secondary)                │
│  ┌─────────────────────────────────────┐ │
│  │           查看详情                  │ │
│  │    [白色背景，#4A90E2边框和字色]     │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🔘 文字按钮 (Text)                     │
│  ┌─────────────────────────────────────┐ │
│  │           跳过练习                  │ │
│  │      [透明背景，#4A90E2字色]        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🔘 危险按钮 (Danger)                   │
│  ┌─────────────────────────────────────┐ │
│  │            删除                     │ │
│  │       [#F5222D背景，白字]          │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🔘 成功按钮 (Success)                  │
│  ┌─────────────────────────────────────┐ │
│  │            确认                     │ │
│  │       [#52C41A背景，白字]          │ │
│  └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

### 4.2 卡片组件展示
```
┌─────────────────────────────────────────┐
│                                        │
│  📄 基础卡片                            │
│  ┌─────────────────────────────────────┐ │
│  │                                    │ │
│  │  📚 三年级上册 - 第一单元            │ │
│  │                                    │ │
│  │  已完成：8/12课程                   │ │
│  │  ████████░░░░ 67%                  │ │
│  │                                    │ │
│  │              [继续学习] >           │ │
│  │                                    │ │
│  └─────────────────────────────────────┘ │
│  [圆角8px，阴影，白色背景]               │
│                                        │
│  📄 状态卡片                            │
│  ┌─────────────────────────────────────┐ │
│  │ ✅                                 │ │
│  │  听写练习已完成                      │ │
│  │                                    │ │
│  │  得分：85分  用时：05:23             │ │
│  │  正确率：85%  等级：⭐⭐⭐⭐        │ │
│  │                                    │ │
│  │         [查看详情] [再来一次]        │ │
│  │                                    │ │
│  └─────────────────────────────────────┘ │
│  [左侧彩色边框，浅绿色背景]              │
│                                        │
└─────────────────────────────────────────┘
```

### 4.3 输入组件展示
```
┌─────────────────────────────────────────┐
│                                        │
│  📝 文本输入框                          │
│  ┌─────────────────────────────────────┐ │
│  │ 请输入用户名...                     │ │
│  └─────────────────────────────────────┘ │
│  [圆角4px，浅灰边框，聚焦时蓝色边框]     │
│                                        │
│  🔍 搜索输入框                          │
│  ┌─────────────────────────────────────┐ │
│  │ 🔍 搜索字词或课程...                │ │
│  └─────────────────────────────────────┘ │
│  [圆角20px，浅灰背景，放大镜图标]        │
│                                        │
│  ✏️ 手写输入区域                        │
│  ┌─────────────────────────────────────┐ │
│  │                                    │ │
│  │          田字格手写区                │ │
│  │                                    │ │
│  │  ┌─────────┐ ┌─────────┐            │ │
│  │  │   苹    │ │         │            │ │
│  │  │ ┼   ┼   │ │ ┼   ┼   │            │ │
│  │  │ ─   ─   │ │ ─   ─   │            │ │
│  │  │ ┼   ┼   │ │ ┼   ┼   │            │ │
│  │  └─────────┘ └─────────┘            │ │
│  │                                    │ │
│  └─────────────────────────────────────┘ │
│  [田字格背景，触摸绘制，实时识别]         │
│                                        │
└─────────────────────────────────────────┘
```

### 4.4 进度组件展示
```
┌─────────────────────────────────────────┐
│                                        │
│  📊 线性进度条                          │
│  学习进度 15/20 (75%)                   │
│  ██████████████████░░░░░               │
│  [蓝色填充，灰色背景，圆角]              │
│                                        │
│  ⭕ 环形进度                            │
│      ┌─────────┐                       │
│      │   85%   │ 本周完成度              │
│      │  ███    │                       │
│      │ █   █   │                       │
│      │  ███    │                       │
│      └─────────┘                       │
│  [绿色渐变，动画效果]                    │
│                                        │
│  📈 阶梯进度                            │
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐         │
│  │ ✅│ │ ✅│ │ ✅│ │ 🔄│ │ ⭕│         │
│  │ 1 │ │ 2 │ │ 3 │ │ 4 │ │ 5 │         │
│  └───┘ └───┘ └───┘ └───┘ └───┘         │
│  [已完成/进行中/未开始三种状态]           │
│                                        │
└─────────────────────────────────────────┘
```

## 5. 图标系统展示

### 5.1 功能图标
```
┌─────────────────────────────────────────┐
│                                        │
│  🎯 学习功能图标                        │
│                                        │
│  📚 教材  ✏️ 练习  📝 听写  🎯 挑战     │
│  🏆 成就  ⭐ 等级  📊 统计  ⚙️ 设置     │
│  👤 个人  🏠 首页  🔍 搜索  📖 词库     │
│                                        │
│  🎵 音效功能图标                        │
│                                        │
│  🔊 播放  ⏸️ 暂停  🔄 重播  ⏭️ 跳过     │
│  🔇 静音  📢 音量  🎧 耳机  🎤 录音     │
│                                        │
│  ✅ 状态图标                            │
│                                        │
│  ✅ 正确  ❌ 错误  ⚠️ 警告  ℹ️ 信息     │
│  🔥 热门  ⭐ 收藏  💡 提示  🎁 奖励     │
│                                        │
└─────────────────────────────────────────┘
```

### 5.2 表情图标
```
┌─────────────────────────────────────────┐
│                                        │
│  😊 学习情绪图标                        │
│                                        │
│  😊 开心  😄 兴奋  🤔 思考  😮 惊讶     │
│  😔 沮丧  😤 努力  😍 喜爱  🥳 庆祝     │
│  🤗 鼓励  👏 赞扬  💪 加油  🎉 成功     │
│                                        │
│  🏆 成就徽章图标                        │
│                                        │
│  🥇 第一  🏆 冠军  ⭐ 明星  💎 钻石     │
│  🎖️ 勋章  👑 王冠  🌟 新星  🔥 火焰     │
│  📖 学者  ✨ 闪亮  💫 流星  🌈 彩虹     │
│                                        │
└─────────────────────────────────────────┘
```

## 6. 动效设计展示

### 6.1 页面转场动效
```
┌─────────────────────────────────────────┐
│                                        │
│  🎬 页面转场动画                        │
│                                        │
│  ➡️ 滑入动画 (300ms)                   │
│     新页面从右侧滑入                    │
│     [缓动函数：ease-out]                │
│                                        │
│  ⬆️ 弹出动画 (250ms)                   │
│     弹窗从底部向上弹出                  │
│     [缓动函数：ease-in-out]             │
│                                        │
│  🔄 淡入动画 (200ms)                   │
│     内容淡入显示                        │
│     [透明度：0 → 1]                    │
│                                        │
└─────────────────────────────────────────┘
```

### 6.2 交互反馈动效
```
┌─────────────────────────────────────────┐
│                                        │
│  🎯 按钮交互动画                        │
│                                        │
│  📱 点击反馈 (100ms)                   │
│     按下：缩放0.95 + 阴影减少           │
│     抬起：恢复原状 + 阴影恢复           │
│                                        │
│  🌊 水波纹效果 (400ms)                 │
│     点击位置扩散圆形波纹                │
│     [透明度：0.3 → 0]                  │
│                                        │
│  ✨ 成功动画 (600ms)                   │
│     勾选图标 + 绿色光晕 + 缩放弹跳      │
│     [缩放：1 → 1.2 → 1]                │
│                                        │
└─────────────────────────────────────────┘
```

### 6.3 学习进度动效
```
┌─────────────────────────────────────────┐
│                                        │
│  📊 进度条动画 (800ms)                  │
│     从左到右逐渐填充                    │
│     [宽度：0% → 目标百分比]             │
│                                        │
│  🎯 分数增长动画 (1200ms)               │
│     数字逐步增长到目标分数              │
│     [数值：0 → 85，步长递减]            │
│                                        │
│  ⭐ 星级评价动画 (500ms × 星数)         │
│     星星依次亮起，带缩放效果            │
│     [缩放：0 → 1.2 → 1，间隔100ms]     │
│                                        │
│  🎁 奖励动画 (1000ms)                  │
│     礼盒弹跳 + 粒子爆炸 + 奖品显示      │
│     [多阶段组合动画]                    │
│                                        │
└─────────────────────────────────────────┘
```

## 7. 布局规范展示

### 7.1 页面布局结构
```
┌─────────────────────────────────────────┐
│ 📱 标准页面布局                         │
│                                        │
│ ┌─────────────────────────────────────┐ │
│ │          导航栏 (44px)              │ │
│ │  ←  页面标题           🔍 ⚙️       │ │
│ └─────────────────────────────────────┘ │
│                                        │
│ ┌─────────────────────────────────────┐ │
│ │                                    │ │
│ │            内容区域                 │ │
│ │         (可滚动区域)                │ │
│ │                                    │ │
│ │  • 16px 左右边距                   │ │
│ │  • 组件间距 12px                   │ │
│ │  • 卡片内边距 16px                 │ │
│ │                                    │ │
│ └─────────────────────────────────────┘ │
│                                        │
│ ┌─────────────────────────────────────┐ │
│ │         底部操作区 (60px)           │ │
│ │    [取消]        [确认]            │ │
│ └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

### 7.2 卡片内容布局
```
┌─────────────────────────────────────────┐
│                                        │
│  📄 信息卡片布局规范                    │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 16px ↕                             │ │
│  │  📚 标题文字 (18px/粗体)            │ │
│  │  8px ↕                             │ │
│  │  描述信息 (14px/常规)  状态标签      │ │
│  │  12px ↕                            │ │
│  │  ┌─────────┐ ┌─────────┐            │ │
│  │  │  按钮1   │ │  按钮2   │  8px ↔   │ │
│  │  └─────────┘ └─────────┘            │ │
│  │ 16px ↕                             │ │
│  └─────────────────────────────────────┘ │
│  ← 16px → 内容区域 ← 16px →             │
│                                        │
└─────────────────────────────────────────┘
```

### 7.3 列表布局规范
```
┌─────────────────────────────────────────┐
│                                        │
│  📋 列表项布局                          │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🎯 图标   主标题                  > │ │
│  │    16px   副标题/描述信息            │ │
│  │           状态信息                  │ │
│  └─────────────────────────────────────┘ │
│  ← 分割线 1px，左右各16px边距 →         │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ✅ 图标   主标题                  > │ │
│  │    16px   副标题/描述信息            │ │
│  │           状态信息                  │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  • 行高：最小60px                      │
│  • 图标大小：24px × 24px               │
│  • 文字左对齐，图标垂直居中             │
│                                        │
└─────────────────────────────────────────┘
```

## 8. 响应式设计展示

### 8.1 不同设备适配
```
┌─────────────────────────────────────────┐
│                                        │
│  📱 iPhone (375px)                     │
│  ┌─────────────────────┐               │
│  │ 单列布局            │               │
│  │ ┌─────────────────┐ │               │
│  │ │     卡片1       │ │               │
│  │ └─────────────────┘ │               │
│  │ ┌─────────────────┐ │               │
│  │ │     卡片2       │ │               │
│  │ └─────────────────┘ │               │
│  └─────────────────────┘               │
│                                        │
│  📱 iPad (768px)                       │
│  ┌─────────────────────────────────────┐ │
│  │ 双列布局                            │ │
│  │ ┌─────────┐ ┌─────────┐             │ │
│  │ │  卡片1   │ │  卡片2   │             │ │
│  │ └─────────┘ └─────────┘             │ │
│  │ ┌─────────┐ ┌─────────┐             │ │
│  │ │  卡片3   │ │  卡片4   │             │ │
│  │ └─────────┘ └─────────┘             │ │
│  └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

### 8.2 横竖屏适配
```
┌─────────────────────────────────────────┐
│                                        │
│  📱 竖屏模式 (常规布局)                 │
│  ┌─────────────────────┐               │
│  │      导航栏         │               │
│  │─────────────────────│               │
│  │                    │               │
│  │      内容区域       │               │
│  │                    │               │
│  │─────────────────────│               │
│  │     底部操作        │               │
│  └─────────────────────┘               │
│                                        │
│  📱 横屏模式 (听写练习专用)             │
│  ┌─────────────────────────────────────┐ │
│  │ 导航│      手写区域      │ 工具栏  │ │
│  │ 栏  │                  │        │ │
│  │    │  ┌─────────┐     │  🔊    │ │
│  │ ←  │  │         │     │  ✏️    │ │
│  │    │  │田字格    │     │  🗑️    │ │
│  │    │  │         │     │  ✅    │ │
│  │    │  └─────────┘     │        │ │
│  └─────────────────────────────────────┘ │
│                                        │
└─────────────────────────────────────────┘
```

## 9. 主题风格变化

### 9.1 日间模式
```
┌─────────────────────────────────────────┐
│                                        │
│  ☀️ 日间模式配色                        │
│                                        │
│  • 背景色：#FFFFFF (纯白)               │
│  • 卡片背景：#FAFAFA (浅灰)             │
│  • 文字色：#262626 (深灰)               │
│  • 边框色：#E5E5E5 (浅边框)             │
│  • 主色调：#4A90E2 (蓝色)               │
│                                        │
│  特点：清爽明亮，护眼舒适                │
│                                        │
└─────────────────────────────────────────┘
```

### 9.2 夜间模式（备选）
```
┌─────────────────────────────────────────┐
│                                        │
│  🌙 夜间模式配色                        │
│                                        │
│  • 背景色：#1A1A1A (深黑)               │
│  • 卡片背景：#2A2A2A (深灰)             │
│  • 文字色：#E5E5E5 (浅灰)               │
│  • 边框色：#404040 (中灰)               │
│  • 主色调：#5BA3F5 (亮蓝)               │
│                                        │
│  特点：降低蓝光，保护视力                │
│  注：考虑学习场景，暂不优先实现          │
│                                        │
└─────────────────────────────────────────┘
```

## 10. 可访问性设计

### 10.1 文字可读性
```
┌─────────────────────────────────────────┐
│                                        │
│  👁️ 可读性标准                         │
│                                        │
│  • 最小字号：12px                      │
│  • 正文字号：16px (推荐)               │
│  • 标题字号：≥18px                     │
│  • 行高：1.4-1.6倍                    │
│  • 字重：常规400，重要700               │
│                                        │
│  • 对比度：≥4.5:1 (AA级别)             │
│  • 重要信息：≥7:1 (AAA级别)            │
│                                        │
└─────────────────────────────────────────┘
```

### 10.2 触摸目标规范
```
┌─────────────────────────────────────────┐
│                                        │
│  👆 触摸目标标准                        │
│                                        │
│  • 最小尺寸：44px × 44px               │
│  • 推荐尺寸：48px × 48px               │
│  • 间距：≥8px                         │
│                                        │
│  ┌────────┐ ┌────────┐ ┌────────┐     │
│  │  按钮1  │ │  按钮2  │ │  按钮3  │     │
│  │ 48×48  │ │ 48×48  │ │ 48×48  │     │
│  └────────┘ └────────┘ └────────┘     │
│     8px      8px                      │
│                                        │
│  特别适配：儿童手指操作                  │
│                                        │
└─────────────────────────────────────────┘
```

## 11. 品牌一致性

### 11.1 Logo应用规范
```
┌─────────────────────────────────────────┐
│                                        │
│  🏷️ Logo应用规范                       │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │  🎯 字词听写                        │ │
│  │     Write & Listen                 │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  • 主Logo：图标 + 中文名称              │
│  • 简化版：仅图标                      │
│  • 最小尺寸：24px × 24px               │
│  • 安全距离：Logo高度的1/2             │
│                                        │
│  使用场景：                            │
│  • 启动页：大尺寸居中                  │
│  • 导航栏：简化版左对齐                │
│  • 关于页：完整版本                    │
│                                        │
└─────────────────────────────────────────┘
```

### 11.2 文案风格
```
┌─────────────────────────────────────────┐
│                                        │
│  📝 文案风格指南                        │
│                                        │
│  🎯 写作原则：                          │
│  • 简洁明了，避免复杂术语              │
│  • 积极正面，鼓励性语言                │
│  • 亲切友好，符合儿童认知              │
│  • 一致性强，统一称呼和表达            │
│                                        │
│  📝 常用文案示例：                      │
│  • 成功："太棒了！"、"继续加油！"       │
│  • 鼓励："再试一次"、"你能行的！"       │
│  • 提示："小贴士"、"记住哦"            │
│  • 错误："没关系，再练习一下"          │
│                                        │
│  🚫 避免用词：                          │
│  • 负面词汇：失败、错误、差劲等         │
│  • 复杂术语：算法、数据库、系统等       │
│  • 成人化表达：请注意、务必、严禁等     │
│                                        │
└─────────────────────────────────────────┘
```

## 12. 设计资源规范

### 12.1 图片资源规范
```
┌─────────────────────────────────────────┐
│                                        │
│  🖼️ 图片资源标准                        │
│                                        │
│  📱 图标规格：                          │
│  • 小图标：24px × 24px                 │
│  • 中图标：32px × 32px                 │
│  • 大图标：48px × 48px                 │
│  • 格式：SVG (矢量) 或 PNG (高清)       │
│                                        │
│  🎨 插画规格：                          │
│  • 横幅：750px × 300px                 │
│  • 卡片：300px × 200px                 │
│  • 背景：1334px × 750px                │
│  • 格式：PNG/JPG，压缩优化             │
│                                        │
│  👤 头像规格：                          │
│  • 用户头像：120px × 120px             │
│  • 缩略头像：60px × 60px               │
│  • 格式：JPG/PNG，圆形裁剪             │
│                                        │
└─────────────────────────────────────────┘
```

### 12.2 动画资源规范
```
┌─────────────────────────────────────────┐
│                                        │
│  🎬 动画资源标准                        │
│                                        │
│  ⚡ 性能要求：                          │
│  • 帧率：30fps (流畅)                  │
│  • 时长：≤2秒 (短小精悍)               │
│  • 文件大小：≤500KB                    │
│  • 格式：GIF/Lottie JSON               │
│                                        │
│  🎯 动画类型：                          │
│  • 加载动画：旋转、跳动                │
│  • 成功动画：勾选、星星                │
│  • 奖励动画：烟花、彩带                │
│  • 过渡动画：滑动、淡入                │
│                                        │
│  📝 命名规范：                          │
│  • loading_spinner.json                │
│  • success_checkmark.gif               │
│  • reward_fireworks.json               │
│                                        │
└─────────────────────────────────────────┘
```

这个界面风格展示文档全面展现了小学字词听写软件的UI设计风格，包括色彩系统、字体规范、组件设计、布局标准、动效规范等各个方面，为开发团队提供了完整的视觉设计指导。设计风格既保持了专业性，又充满了适合小学生的活泼童趣，确保软件既好用又好看。 
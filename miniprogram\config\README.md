# 教材配置文件说明

本文件夹包含小学语文教材的字词配置文件，用于听写练习小程序。

## 文件结构

- `textbooks.json` - 主索引文件，包含所有教材版本、年级和学期的基本信息
- `renjiaoban.json` - 人教版教材详细内容
- `sujiaoba.json` - 苏教版教材详细内容
- `beishidaban.json` - 北师大版教材详细内容

## 数据结构说明

### 主索引文件 (textbooks.json)

包含所有支持的教材版本列表，以及每个版本下的年级和学期信息。

```json
{
  "versions": [
    {
      "id": "版本ID",
      "name": "版本名称",
      "file": "对应的详细文件名",
      "grades": [
        {
          "id": "年级ID",
          "name": "年级名称",
          "terms": [
            {
              "id": "学期ID",
              "name": "学期名称"
            }
          ]
        }
      ]
    }
  ]
}
```

### 教材详细内容文件 (*.json)

每个教材版本的详细内容，包含每个年级、学期下的所有课程和对应的识字表、写字表和词语表。

```json
{
  "id": "版本ID",
  "name": "版本名称",
  "grades": {
    "年级ID": {
      "name": "年级名称",
      "terms": {
        "学期ID": {
          "name": "学期名称",
          "lessons": [
            {
              "id": "课程ID",
              "name": "课程名称",
              "title": "课程标题",
              "tables": {
                "shiZiBiao": [
                  { "word": "字", "pinyin": "拼音" }
                ],
                "xieZiBiao": [
                  { "word": "字", "pinyin": "拼音" }
                ],
                "ciYuBiao": [
                  { "word": "词语", "pinyin": "拼音" }
                ]
              }
            }
          ]
        }
      }
    }
  }
}
```

## 使用方法

1. 加载主索引文件 `textbooks.json` 获取所有教材版本列表
2. 用户选择版本、年级和学期后，加载对应的详细内容文件（如 `renjiaoban.json`）
3. 根据用户选择的年级和学期，展示对应的课程列表
4. 用户选择课程后，可以选择使用识字表、写字表或词语表进行听写练习

## 添加新内容

1. 在详细内容文件中按照格式添加新的课程和词表
2. 确保所有ID和索引关系正确
3. 注意保持JSON格式有效

## 字段说明

- `id`: 唯一标识符
- `name`: 显示名称
- `file`: 对应的详细文件名
- `grades`: 年级列表
- `terms`: 学期列表
- `lessons`: 课程列表
- `title`: 课程标题
- `tables`: 包含三种表格：识字表(shiZiBiao)、写字表(xieZiBiao)和词语表(ciYuBiao)
- `word`: 汉字或词语
- `pinyin`: 对应的拼音，多音字用主要读音 